"""Web API 模块 - 提供 FastAPI 接口"""
import asyncio
import json
import logging
import os
import uuid
from contextvars import Token
from datetime import datetime
from queue import Queue as ThreadQueue
from typing import Optional, List

from fastapi import HTTPException
from fastapi.responses import HTMLResponse, StreamingResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field

from app import create_app
from app.application.app import get_assistant_app, shared_executor
from app.controllers.knowledge_controller import router as knowledge_router
from app.controllers.pv_controller import router as pv_router
from app.controllers.stream_controller import router as stream_router
from app.domain.schemas import (
    AssistantAppParams, StreamEvent, StreamEventType, IntentInfo, CustomApiResponse, StandardResponse, CrfIndexAnswer,
    CrfTableAnswerValue, CrfTableResult, KnowledgeSliceItem
)
from app.utils.direct_llm import stream_queue_var, call_llm_api, call_direct_llm_api

# Configure logging with detailed timestamp
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 获取静态文件目录的绝对路径
static_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), "static")

# 增加AnyIO线程池大小，提高并发处理能力
from anyio.lowlevel import RunVar
from anyio import CapacityLimiter
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app_context):
    """应用生命周期管理，启动时增加线程池大小，提高并发处理能力"""
    # 启动时执行
    logger.info("应用启动：增加AnyIO线程池大小到100，提高并发处理能力")
    RunVar("_default_thread_limiter").set(CapacityLimiter(100))
    yield
    # 关闭时执行
    logger.info("应用关闭：清理资源")

# 创建FastAPI应用，添加lifespan
app = create_app(lifespan=lifespan)

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory=static_dir), name="static")


# 定义工具列表项模型
class ToolInfo(BaseModel):
    name: str
    description: Optional[str] = None


# 定义文本分析请求模型
class TextAnalysisRequest(BaseModel):
    input_text: str = Field(..., description="需要分析的输入文本")


# 定义文本分析响应模型
class TextAnalysisResponse(BaseModel):
    output_text: str
    thinking_process: Optional[str] = None


# 定义标准格式的文本分析响应
class StandardTextAnalysisResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: TextAnalysisResponse


@app.get("/", response_class=HTMLResponse)
async def get_root():
    """返回前端HTML页面"""
    if not os.path.exists(static_dir) or not os.path.exists(os.path.join(static_dir, "index.html")):
        logger.error(f"Static directory or index.html not found at expected path: {static_dir}")
        raise HTTPException(status_code=500, detail="Server configuration error: Static file not found.")
    try:
        with open(os.path.join(static_dir, "index.html"), "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading index.html: {e}")
        raise HTTPException(status_code=500, detail="Server error reading main page.")


import uuid
from fastapi.responses import JSONResponse


async def event_generator(queue: asyncio.Queue, run_task: asyncio.Task, relay_task: asyncio.Task):
    logger.info("Starting event generator...")
    try:
        while True:
            try:
                # Wait for an event from the SSE queue with a timeout
                event_json = await asyncio.wait_for(queue.get(), timeout=60.0)  # Example timeout
                if event_json is None:  # Check for sentinel value
                    logger.info("Received sentinel value, breaking event generator loop.")
                    break

                # Deserialize the event
                try:
                    event_data = json.loads(event_json)
                    event_type = event_data.get("event")
                    event_id = event_data.get("id")
                    logger.debug(f"Event Generator: Got event {event_type} (ID: {event_id})")

                    # Format as Server-Sent Event
                    # Use 'data:' prefix. Ensure data is properly JSON encoded if complex.
                    sse_data = f"id: {event_id}\nevent: {event_type}\ndata: {event_json}\n\n"
                    yield sse_data

                    # Check if this is the final event (COMPLETE or ERROR)
                    if event_type in [StreamEventType.COMPLETE.value, StreamEventType.ERROR.value]:
                        logger.info(f"Event Generator: Received final event '{event_type}'. Stopping.")
                        break  # Exit loop after sending final event

                except json.JSONDecodeError:
                    logger.error(f"Event Generator: Failed to decode JSON: {event_json}")
                    continue  # Skip malformed events
                except Exception as e:
                    logger.error(f"Event Generator: Error processing event: {e}", exc_info=True)
                    # Optionally yield an error event to the client here
                    break  # Stop on unexpected errors

            except asyncio.TimeoutError:
                logger.warning("Event Generator: Timeout waiting for event from queue.")
                # Decide if timeout should stop the generator or just continue waiting
                # If the stream should end on timeout, uncomment the next line:
                # break
                # Send a keep-alive comment (optional)
                yield ": keep-alive\n\n"
                continue
            except asyncio.CancelledError:
                logger.info("Event Generator: Task cancelled.")
                raise  # Re-raise CancelledError to ensure cleanup
            finally:
                # This ensures queue.task_done() is called even if errors occur above
                # but before the break/continue/raise
                if 'event_json' in locals() and event_json is not None:
                    # Only call task_done if we actually got an item
                    try:
                        queue.task_done()
                        logger.debug("Event Generator: queue.task_done() called.")
                    except ValueError:
                        # Handle case where task_done is called too many times (e.g., if already called)
                        logger.warning("Event Generator: queue.task_done() called too many times or on empty queue.")

        logger.info("Event generator loop finished normally.")

    except asyncio.CancelledError:
        logger.info("Event Generator caught CancelledError during operation.")
        # Propagate cancellation if needed, or handle cleanup
        raise
    except Exception as e:
        logger.error(f"Event Generator: Unhandled exception: {e}", exc_info=True)
        # Optionally yield a final error event to the client
        try:
            error_event = StreamEvent(id="internal_error", event=StreamEventType.ERROR,
                                      content=f"Internal generator error: {e}")
            sse_error_data = f"id: {error_event.id}\nevent: {error_event.event.value}\ndata: {error_event.model_dump_json()}\n\n"
            yield sse_error_data
        except Exception as final_err:
            logger.error(f"Event Generator: Failed to yield final error event: {final_err}")
    finally:
        logger.info("Event Generator: Entering finally block for cleanup.")
        # Cancel the background tasks if they are still running
        if run_task and not run_task.done():
            run_task.cancel()
            logger.info("Event Generator: Cancelled run_graph_and_process task.")
        else:
            logger.info("Event Generator: run_graph_and_process task already done or not provided.")

        if relay_task and not relay_task.done():
            relay_task.cancel()
            logger.info("Event Generator: Cancelled relay_queues task.")
        else:
            logger.info("Event Generator: relay_queues task already done or not provided.")

        # Wait briefly for tasks to finish cancellation (optional but good practice)
        # try:
        #     await asyncio.gather(run_task, relay_task, return_exceptions=True)
        #     logger.info("Event Generator: Background tasks awaited after cancellation.")
        # except asyncio.CancelledError:
        #      logger.info("Event Generator: Cancellation occurred while awaiting background tasks.")
        # except Exception as gather_exc:
        #      logger.error(f"Event Generator: Error awaiting background tasks: {gather_exc}")

        logger.info("Event Generator finished cleanup.")


async def run_graph_and_process(params: AssistantAppParams, llm_q: ThreadQueue, sse_q: asyncio.Queue):
    context_token: Optional[Token] = None
    final_result = None
    error_message = None
    # Use incoming requestId if available, otherwise generate a UUID
    message_id = params.metaInfo.requestId if hasattr(params,
                                                      'requestId') and params.metaInfo.requestId else f"msg_{uuid.uuid4().hex[:6]}"
    logger.info(f"[run_graph] Starting processing for request {message_id}.")

    try:
        # Set the queue in context for call_llm_api
        context_token = stream_queue_var.set(llm_q)
        logger.info(f"[run_graph] ContextVar stream_queue_var set for request {message_id}.")

        # Get the LangGraph app instance
        try:
            logger.debug(f"[run_graph] Getting assistant app instance for {message_id}...")
            current_app, _ = get_assistant_app(params)
            logger.debug(f"[run_graph] Got assistant app instance for {message_id}.")
        except (ValueError, RuntimeError) as app_init_exc:
            logger.error(f"[run_graph] Failed to initialize assistant app for {message_id}: {app_init_exc}",
                         exc_info=True)
            # Put error into queue instead of raising HTTPException here
            error_message = f"Assistant App Initialization Error: {app_init_exc}"
            raise  # Re-raise to be caught by the outer try-except

        # Wrap the blocking LangGraph invocation
        logger.info(f"[run_graph] Invoking LangGraph app in thread for request {message_id}...")
        # Correct input format: Pass the entire params object nested under 'app_params'
        input_data = {"app_params": params}
        logger.debug(
            f"[run_graph] Prepared input_data for invoke: {{'app_params': <AssistantAppParams object>}} for {message_id}.")

        final_result = await asyncio.to_thread(
            current_app.invoke,
            input_data  # Pass the correctly structured input
        )
        logger.info(f"[run_graph] LangGraph app invocation finished for request {message_id}.")
        logger.debug(f"[run_graph] Final result type for {message_id}: {type(final_result)}")
        logger.debug(
            f"[run_graph] Final result content (partial) for {message_id}: {str(final_result)[:500]}...")  # Log partial result

    except Exception as e:
        logger.error(f"[run_graph] Error during LangGraph execution or processing for request {message_id}: {e}",
                     exc_info=True)
        error_message = f"Error during processing: {str(e)}"
    finally:
        # Reset context variable IMPORTANT!
        logger.info(f"[run_graph] Entering finally block for request {message_id}.")
        if context_token:
            stream_queue_var.reset(context_token)
            logger.info(f"[run_graph] ContextVar stream_queue_var reset for request {message_id}.")
        else:
            logger.warning(f"[run_graph] Context token was None, could not reset context var for {message_id}.")

        # Put final event into SSE queue
        logger.debug(f"[run_graph] Preparing final event for {message_id}...")
        if error_message:
            final_event = StreamEvent(id=message_id, event=StreamEventType.ERROR, content=error_message)
            logger.info(f"[run_graph] Final event is ERROR for {message_id}: {error_message}")
        else:
            # Process final_result to include in the 'data' field of the COMPLETE event
            # --- Replicate standard response formatting for the COMPLETE event data --- START
            logger.info(f"[run_graph] Formatting final result for COMPLETE event data for {message_id}...")
            try:
                now = datetime.now()
                request_id = message_id # Use the message_id as request_id for the event
                timestamp = now.strftime("%Y-%m-%dT%H:%M:%SZ")

                # Ensure final_result is a dict, otherwise create error message
                if not isinstance(final_result, dict):
                    logger.error(f"[run_graph] Final result is not a dict for {message_id}. Type: {type(final_result)}")
                    error_message = "Internal error: Unexpected format for final result."
                    final_event = StreamEvent(id=message_id, event=StreamEventType.ERROR, content=error_message)
                else:
                    answer = final_result.get('final_response', "")
                    intent_info = IntentInfo(
                        primary="表单填写", # Assuming primary intent, adjust if needed
                        secondary=final_result.get('icrc_intent', None),
                        confidence=None, # Confidence typically not available here
                        modelName=os.getenv("LLM_MODEL_NAME")
                    )

                    # Call helper functions to format data
                    crf_results = getCrfResults(final_result)
                    knowledge_slices = getKnowledage(final_result)

                    thought_log = final_result.get('thought_log', [])
                    think_logics_str = "\n".join(thought_log) if thought_log and isinstance(thought_log, list) else None

                    # Get visits from original params
                    visits = params.metaInfo.visits if params and hasattr(params, 'metaInfo') and params.metaInfo and hasattr(params.metaInfo, 'visits') else None

                    # Construct the full API response object
                    api_response_data = CustomApiResponse(
                        request_id=request_id,
                        timestamp=timestamp,
                        intent=intent_info,
                        answer=answer,
                        crf_result=crf_results,
                        knowledge_slice=knowledge_slices,
                        thinkLogic=think_logics_str,
                        visits=visits
                    )

                    # Create the COMPLETE event with the full data object
                    # model_dump() preferred over dict()
                    complete_data_payload = api_response_data.model_dump() if hasattr(api_response_data, 'model_dump') else api_response_data.dict()
                    final_event = StreamEvent(id=message_id, event=StreamEventType.COMPLETE, data=complete_data_payload)
                    logger.info(f"[run_graph] Final event is COMPLETE for {message_id} with full data payload.")

            except Exception as format_exc:
                 logger.error(f"[run_graph] Error formatting final result for COMPLETE event {message_id}: {format_exc}", exc_info=True)
                 error_message = f"Internal error: Failed to format final result: {format_exc}"
                 final_event = StreamEvent(id=message_id, event=StreamEventType.ERROR, content=error_message)
            # --- Replicate standard response formatting for the COMPLETE event data --- END

        # Ensure queue is still available before putting
        if not sse_q.full():
            try:
                await sse_q.put(final_event.model_dump_json())
            except Exception as put_err:
                logger.error(f"[run_graph] Error putting final event into sse_queue for {message_id}: {put_err}",
                             exc_info=True)
        else:
            logger.error(f"[run_graph] SSE queue full, cannot put final event for {message_id}.")

        # Signal end of LLM stream processing (add a sentinel value to thread queue)
        try:
            logger.debug(f"[run_graph] Attempting to put sentinel None into llm_stream_queue for {message_id}.")
            llm_q.put_nowait(None)  # Sentinel to stop the queue relay
            logger.info(f"[run_graph] Sentinel None put into llm_stream_queue for {message_id}.")
        except ThreadQueue.Full:
            logger.error(f"[run_graph] LLM stream queue full, cannot put sentinel for {message_id}.")
        except Exception as sentinel_err:
            logger.error(f"[run_graph] Error putting sentinel into llm_stream_queue for {message_id}: {sentinel_err}",
                         exc_info=True)

        logger.info(f"[run_graph] Exiting finally block for {message_id}.")


async def relay_queues(llm_q: ThreadQueue, sse_q: asyncio.Queue):
    loop = asyncio.get_running_loop()
    logger.info("[relay_queues] Relay task started.")
    while True:
        item = None
        try:
            # Use loop.run_in_executor to get from the thread-safe queue without blocking async loop
            logger.debug("[relay_queues] Waiting to get item from llm_stream_queue...")
            item = await loop.run_in_executor(None, llm_q.get)
            # Log item type carefully, avoid logging large content
            item_log_info = 'SENTINEL (None)' if item is None else f"type {type(item).__name__}"
            logger.debug(f"[relay_queues] Got item from llm_stream_queue: {item_log_info}")

            if item is None:  # Sentinel value received
                logger.info("[relay_queues] Sentinel received, stopping relay.")
                llm_q.task_done()  # Mark sentinel as done
                break
            # Ensure SSE queue is available
            if not sse_q.full():
                logger.debug("[relay_queues] Attempting to put item into sse_queue...")
                await sse_q.put(item)
                logger.debug("[relay_queues] Successfully put item into sse_queue.")
            else:
                logger.warning("[relay_queues] SSE queue is full, discarding item from LLM stream queue.")
            llm_q.task_done()  # Mark task done for the thread queue
        except Exception as e:
            logger.error(f"[relay_queues] Error getting or putting item: {e}", exc_info=True)
            # If error occurs, stop relaying to prevent infinite loops
            if item is not None:  # Ensure task_done is called if item was retrieved
                try:
                    llm_q.task_done()
                    logger.debug("[relay_queues] Marked task done after error.")
                except ValueError:
                    logger.warning("[relay_queues] task_done() called too many times after error.")
            break  # Exit relay loop on error
    logger.info("[relay_queues] Relay task finished.")


@app.post("/api/chat")  # Keep existing path
async def chat(request: AssistantAppParams):
    """处理聊天请求 - 支持标准和流式响应"""
    try:
        params = request

        # 检查并设置默认值
        params.chatType = params.chatType if params.chatType is not None else "standard"
        params.questions = params.questions if params.questions is not None else []
        # Ensure requestId exists if needed later, potentially generate one if missing
        if not hasattr(params.metaInfo.requestId, 'requestId') or not params.metaInfo.requestId:
            params.metaInfo.requestId = f"req_{uuid.uuid4().hex[:8]}"
            logger.info(f"Generated requestId: {params.metaInfo.requestId}")

        logger.info(
            f"Processing chat request: chatType={params.chatType}, requestId={params.metaInfo.requestId}, source={params.metaInfo.sceneInfo.sceneName if params.metaInfo and params.metaInfo.sceneInfo else 'N/A'}")

        # --- 分支处理流式和非流式请求 ---
        if params.chatType == "stream":
            logger.info(f"Handling stream request for {params.metaInfo.requestId}")
            # 1. 创建队列
            llm_stream_queue = ThreadQueue(maxsize=1000)  # Queue for LLM events (thread-safe) - Increased size
            sse_event_queue = asyncio.Queue(maxsize=1000)  # Queue for SSE events (asyncio) - Increased size

            # 2. 创建后台任务：运行 LangGraph 并处理结果
            run_task = asyncio.create_task(
                run_graph_and_process(params, llm_stream_queue, sse_event_queue)
            )
            logger.debug(f"Created background task run_graph_and_process for request {params.metaInfo.requestId}")

            # 3. 创建后台任务：在两个队列间中继事件
            relay_task = asyncio.create_task(
                relay_queues(llm_stream_queue, sse_event_queue)
            )
            logger.debug(f"Created background task relay_queues for request {params.metaInfo.requestId}")

            # 4. 返回流式响应 (event_generator now takes tasks to handle cleanup)
            logger.info(f"Returning StreamingResponse to client for {params.metaInfo.requestId}.")
            return StreamingResponse(
                event_generator(sse_event_queue, run_task, relay_task),
                media_type="text/event-stream"
            )

        else:  # chatType is "standard" or unspecified
            logger.info(f"Handling standard request for {params.metaInfo.requestId}")
            # --- 执行原始的非流式逻辑 --- (Based on user provided code in Step 52)
            try:
                # 获取助手应用实例
                current_app, _ = get_assistant_app(params)
            except (ValueError, RuntimeError) as app_init_exc:
                logger.error(
                    f"Failed to get assistant app for standard request {params.metaInfo.requestId}: {app_init_exc}",
                    exc_info=True)
                if isinstance(app_init_exc, ValueError):
                    # Use status_code 422 for Unprocessable Entity if params are invalid
                    raise HTTPException(status_code=422, detail=f"Invalid request parameters: {app_init_exc}")
                else:
                    raise HTTPException(status_code=503,
                                        detail="Service temporarily unavailable due to configuration error.")

            if current_app is None:
                logger.error(
                    f"Failed to obtain a valid assistant app instance for standard request {params.metaInfo.requestId}.")
                raise HTTPException(status_code=503, detail="服务暂时不可用，请稍后再试")

            try:
                # 使用异步方式调用应用并等待结果
                logger.info(f"Invoking LangGraph app asynchronously for standard request {params.metaInfo.requestId}...")
                # 使用asyncio.to_thread将阻塞调用放入线程池中执行，避免阻塞事件循环
                result = await asyncio.to_thread(
                    current_app.invoke,
                    {"app_params": params}
                )
                logger.info(f"Standard request processed successfully for request {params.metaInfo.requestId}")

                # 构建新的响应格式 (Copied from user provided code, ensure schemas are imported)
                now = datetime.now()
                request_id = params.metaInfo.requestId  # Use the existing or generated requestId
                timestamp = now.strftime("%Y-%m-%dT%H:%M:%SZ")

                answer = result.get('final_response', "")
                intent_info = IntentInfo(
                    primary="表单填写",
                    secondary=result.get('icrc_intent', None),
                    confidence=None,
                    modelName=os.getenv("LLM_MODEL_NAME")
                )

                # 转换crf_result格式
                crf_results = getCrfResults(result)

                knowledge_slices = getKnowledage(result)

                thought_log = result.get('thought_log', [])
                think_logics_str = "\n".join(thought_log) if thought_log and isinstance(thought_log, list) else None

                visits = params.metaInfo.visits if params and hasattr(params,
                                                                      'metaInfo') and params.metaInfo and hasattr(
                    params.metaInfo, 'visits') else None

                api_response = CustomApiResponse(
                    request_id=request_id,
                    timestamp=timestamp,
                    intent=intent_info,
                    answer=answer,
                    crf_result=crf_results,  # Should be populated by the actual logic above
                    knowledge_slice=knowledge_slices,  # Should be populated by the actual logic above
                    thinkLogic=think_logics_str,
                    visits=visits
                )

                standard_response = StandardResponse(
                    code=200,
                    message="success",
                    data=api_response
                )

                try:
                    if hasattr(standard_response, 'model_dump'):
                        response_data = standard_response.model_dump()
                    else:
                        logger.warning("Using deprecated dict() method, recommend upgrading Pydantic.")
                        response_data = standard_response.dict()
                    logger.info(f"Returning standard JSON response for {params.metaInfo.requestId}.")
                    return JSONResponse(content=response_data)
                except Exception as ser_exc:
                    logger.error(f"Error serializing standard response for {params.metaInfo.requestId}: {ser_exc}",
                                 exc_info=True)
                    # Fallback response if serialization fails
                    return JSONResponse(
                        content={"code": 500, "message": "Error creating response data", "data": {"answer": answer}},
                        status_code=500)

            except Exception as invoke_exc:
                logger.error(
                    f"Error invoking assistant graph for standard request {params.metaInfo.requestId}: {invoke_exc}",
                    exc_info=True)
                raise HTTPException(status_code=500, detail="处理请求时发生内部错误")

    except HTTPException as http_exc:
        # Reraise HTTPExceptions directly
        raise http_exc
    except Exception as e:
        # Catch any other unexpected errors
        request_id_for_log = request.requestId if hasattr(request, 'requestId') else 'unknown'
        logger.error(f"Unexpected error in chat endpoint for requestId {request_id_for_log}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="处理请求时发生意外错误")


def getCrfResults(result):
    crf_results = []
    original_crf = result.get('crf_result')
    # 不再使用visit_results，直接使用original_crf处理循环表单逻辑
    # 检查是否为循环表单
    is_repeat = 0
    if 'app_params' in result:
        app_params = result['app_params']
        if hasattr(app_params, 'metaInfo') and hasattr(app_params.metaInfo, 'isRepeat'):
            is_repeat = app_params.metaInfo.isRepeat
    logger.info(f"Is repeat form: {is_repeat}")
    if original_crf:
        # 创建一个字典，用于存储每个表格的结果
        table_results = {}

        # 初始化answer_id计数器，从1开始递增
        answer_id_counter = 1

        # 首先处理每个表格
        for i, crf_table in enumerate(original_crf):
            # 检查crf_table是否为字典类型，如果是则使用字典访问方式
            if isinstance(crf_table, dict):
                table_code = crf_table.get('crfTableCode')
                logger.info(f"crf_table是字典类型，使用get方法获取crfTableCode: {table_code}")
            else:
                # 否则假设它是一个对象，使用属性访问方式
                table_code = getattr(crf_table, 'crfTableCode', None)
                logger.info(f"crf_table是对象类型，使用属性访问方式获取crfTableCode: {table_code}")

            # 如果table_code为None，生成一个默认值
            if table_code is None:
                table_code = f"unknown_table_{i}"
                logger.warning(f"表格代码为None，使用默认值: {table_code}")

            # 如果这个表格还没有处理过，初始化它
            if table_code not in table_results:
                # 根据crf_table的类型获取表格名称和组ID
                if isinstance(crf_table, dict):
                    table_name = crf_table.get('crfTableName')
                    group_id = crf_table.get('crfGroupId')
                else:
                    table_name = getattr(crf_table, 'crfTableName', None)
                    group_id = getattr(crf_table, 'crfGroupId', None)

                # 如果表格名称为None，生成一个默认值
                if table_name is None:
                    table_name = f"未知表格_{i}"
                    logger.warning(f"表格名称为None，使用默认值: {table_name}")

                if group_id is None:
                    group_id = f"unknown_group_{i}"
                    logger.warning(f"组ID为None，使用默认值: {group_id}")

                table_results[table_code] = {
                    'table_name': table_name,
                    'table_code': table_code,
                    'group_id': group_id,
                    'visits': {}
                }

            # 获取visit_id
            # 从表格对象中获取visitId
            visit_id = ""
            if isinstance(crf_table, dict):
                # 字典类型访问
                if 'visitId' in crf_table and crf_table['visitId']:
                    visit_id = str(crf_table['visitId'])
                    logger.info(f"Using visitId from crf_table dict: {visit_id}")
                else:
                    # 如果字典中没有visitId，则使用索引
                    visit_id = str(i)
                    logger.info(f"Using index as visitId (dict): {visit_id}")
            else:
                # 对象类型访问
                if hasattr(crf_table, 'visitId') and crf_table.visitId:
                    visit_id = str(crf_table.visitId)
                    logger.info(f"Using visitId from crf_table object: {visit_id}")
                else:
                    # 如果对象中没有visitId，则使用索引
                    visit_id = str(i)
                    logger.info(f"Using index as visitId (object): {visit_id}")

            # 如果这个visit还没有处理过，初始化它
            if visit_id not in table_results[table_code]['visits']:
                table_results[table_code]['visits'][visit_id] = []

            # 处理表格中的每个指标
            index_list = None
            if isinstance(crf_table, dict):
                # 字典类型访问
                index_list = crf_table.get('crfIndexList')
            else:
                # 对象类型访问
                index_list = crf_table.crfIndexList if hasattr(crf_table, 'crfIndexList') else None

            if index_list:
                answer_values = []
                for idx in index_list:
                    # 检查idx是否为字典类型
                    if isinstance(idx, dict):
                        # 字典类型访问
                        answer_values.append(CrfIndexAnswer(
                            crf_index_name=idx.get('crfIndexName'),
                            crf_index_code=idx.get('crfIndexCode'),
                            crf_index_value=idx.get('crfIndexValue') or "",
                            dataSource=idx.get('dataSource') if idx.get('dataSource') is not None else []
                        ))
                    else:
                        # 对象类型访问
                        answer_values.append(CrfIndexAnswer(
                            crf_index_name=idx.crfIndexName,
                            crf_index_code=idx.crfIndexCode,
                            crf_index_value=idx.crfIndexValue or "",
                            dataSource=idx.dataSource if idx.dataSource is not None else []
                        ))

                # 将这个visit的结果添加到对应表格的visits中
                # 对于循环表单，我们需要保留所有的结果，而不是覆盖
                # 不再使用is_repeat_item标记，直接使用is_repeat判断

                # 判断是否为循环表单项
                if is_repeat == 1:
                    # 对于循环表单，我们使用唯一的键来避免覆盖
                    # 使用索引作为item_index
                    item_index = i
                    unique_key = f"{visit_id}_{item_index}"
                    table_results[table_code]['visits'][unique_key] = answer_values
                    logger.info(
                        f"Added repeat item {item_index} for visit {visit_id} to table {table_code}")
                else:
                    # 对于非循环表单，直接覆盖
                    table_results[table_code]['visits'][visit_id] = answer_values

                # 打印详细日志，便于调试
                logger.info(
                    f"Current table_results for {table_code}: {len(table_results[table_code]['visits'])} visits")

        # 然后为每个表格创建CrfTableResult
        for table_code, table_info in table_results.items():
            # 为每个visit创建CrfTableAnswerValue
            table_values = []

            # 直接处理每个表格项，不再按visit_id分组
            # 这样可以确保循环表单的每个项都被单独处理
            for key, answer_values in table_info['visits'].items():
                # 确定visit_id
                if "_" in key:
                    # 循环表单项，键的格式是"visit_id_item_index"
                    parts = key.split("_")
                    visit_id = parts[0]
                    item_index = parts[1] if len(parts) > 1 else "0"
                    logger.info(
                        f"Processing repeat item with key {key}, visit_id {visit_id}, item_index {item_index}")
                else:
                    # 非循环表单项
                    visit_id = key
                    logger.info(f"Processing non-repeat item with key {key}")

                # 确保visit_id是字符串类型
                visit_id_str = str(visit_id) if visit_id else ""

                # 只有当answer_values不为空时才添加
                if answer_values:
                    # 将每个项添加为单独的CrfTableAnswerValue，使用递增的answer_id
                    table_values.append(CrfTableAnswerValue(
                        answer_id=answer_id_counter,
                        visit_id=visit_id_str,
                        answer_value=answer_values
                    ))
                    logger.info(
                        f"Added answer_value for visit {visit_id_str} with {len(answer_values)} fields, answer_id={answer_id_counter}")
                    # 递增answer_id计数器
                    answer_id_counter += 1
                else:
                    logger.info(f"Skipped empty answer_value for visit {visit_id_str}")

            # 检查必填字段是否有值
            table_name = table_info['table_name']
            table_code = table_info['table_code']
            group_id = table_info['group_id']

            # 如果必填字段为None，则提供默认值
            if table_name is None:
                table_name = "未知表格"
                logger.warning(f"表格名称为None，使用默认值: {table_name}")

            if table_code is None:
                table_code = f"unknown_table_{len(crf_results)}"
                logger.warning(f"表格代码为None，使用默认值: {table_code}")

            # 创建表格结果
            try:
                crf_results.append(CrfTableResult(
                    crf_table_name=table_name,
                    crf_table_code=table_code,
                    crf_table_value_type=0,  # 默认为卡片类型
                    crf_group_id=group_id,
                    crf_table_value=table_values
                ))
                logger.info(f"成功创建CrfTableResult: name={table_name}, code={table_code}")
            except Exception as e:
                logger.error(f"创建CrfTableResult时出错: {e}")
                # 记录详细的参数信息，便于调试
                logger.error(
                    f"参数详情: name={table_name}({type(table_name)}), code={table_code}({type(table_code)}), group_id={group_id}({type(group_id)}), values_count={len(table_values)}")
    return crf_results


def getKnowledage(result):
    knowledge_slices = []
    original_slices = result.get('knowledge_slice', [])
    if original_slices:
        for i, slice_item in enumerate(original_slices):
            # 将KnowledgeSlicePosition转换为Position
            position_dict = None
            if slice_item.position:
                # 如果是字典格式，直接使用
                if isinstance(slice_item.position, dict):
                    position_dict = slice_item.position
                else:
                    # 如果是KnowledgeSlicePosition对象，转换为字典
                    position_dict = {
                        "context": getattr(slice_item.position, 'context', None),
                        "pageNo": getattr(slice_item.position, 'pageNo', None),
                        "start": getattr(slice_item.position, 'start', 0),
                        "end": getattr(slice_item.position, 'end', 0)
                    }

            # 创建KnowledgeSliceItem对象
            knowledge_slices.append(KnowledgeSliceItem(
                fileId=slice_item.fileId or f"file_{i}",
                fileName=slice_item.fileName or "未知文件",
                fileUrl=slice_item.fileUrl,
                tag=i + 1,
                start=slice_item.start or 0,
                end=slice_item.end or 0,
                context=slice_item.context or "",
                position=position_dict
            ))
    return knowledge_slices


@app.post("/api/analyze_text")
async def analyze_text(request: TextAnalysisRequest):
    """
    文本分析接口 - 输入文本通过大模型分析后返回结果

    Args:
        request: 包含输入文本和可选参数的请求对象

    Returns:
        分析结果，包含输出文本和思考过程
    """
    try:
        logger.info(f"收到文本分析请求，输入长度: {len(request.input_text)}")

        # 使用独立的配置调用大模型API
        analyze_provider = os.getenv("ANALYZE_TEXT_LLM_PROVIDER", os.getenv("LLM_PROVIDER", "deepseek"))
        analyze_model = os.getenv("ANALYZE_TEXT_LLM_MODEL_NAME", os.getenv("LLM_MODEL_NAME"))

        # 构建提示词
        prompt = request.input_text

        # 调用大模型API，使用 asyncio.to_thread 将阻塞调用放到线程中执行
        output_text, thinking_process = await asyncio.to_thread(
            call_direct_llm_api,
            prompt=prompt,
            provider=analyze_provider,
            model=analyze_model,
            temperature=0.0,
        )

        # 构建响应
        text_analysis_response = TextAnalysisResponse(
            output_text=output_text,
            thinking_process=thinking_process
        )

        # 构建标准响应
        standard_response = StandardTextAnalysisResponse(
            code=200,
            message="success",
            data=text_analysis_response
        )

        # 记录日志
        logger.info(f"文本分析完成，输出长度: {len(output_text)}")

        # 返回响应
        try:
            # 尝试使用Pydantic v2的model_dump方法
            if hasattr(standard_response, 'model_dump'):
                response_data = standard_response.model_dump()
            else:
                # 尝试使用Pydantic v1的dict方法，但添加警告日志
                logger.warning("使用已弃用的dict()方法，建议升级到Pydantic v2并使用model_dump()方法")
                response_data = standard_response.dict()

            logger.info(f"Generated API response: {response_data}")
            return JSONResponse(content=response_data)
        except Exception as e:
            logger.error(f"序列化响应时出错: {e}")
            # 如果序列化失败，返回简化的响应
            return JSONResponse(
                content={"code": 200, "message": "success", "data": {"output_text": output_text}},
                status_code=200
            )

    except Exception as e:
        logger.error(f"文本分析过程中发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"处理请求时发生错误: {str(e)}")


@app.get("/api/health")
async def health_check():
    """健康检查接口"""
    return {"status": "ok"}


@app.get("/tools", response_model=List[ToolInfo])
async def list_tools():
    """列出所有可用的工具及其描述"""
    # Use the imported shared_executor
    if shared_executor is None:
        logger.error("Shared tool executor not available.")
        raise HTTPException(status_code=503, detail="Tool executor not available.")

    tool_list = []
    try:
        # Access tools directly from the shared executor instance
        for name, config in shared_executor.tools.items():
            tool_list.append(ToolInfo(name=name, description=config.get('description')))
        return tool_list
    except Exception as e:
        logger.error(f"Error listing tools: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve tool list")


if __name__ == "__main__":
    import uvicorn

    app.include_router(knowledge_router, prefix="/api/v1")
    app.include_router(pv_router)
    app.include_router(stream_router)
    uvicorn.run("app.adapters.api.main:app", host="0.0.0.0", port=8000, reload=True)
