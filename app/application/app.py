"""
助手图模块 - 使用 LangGraph 构建助手流程图
仅处理标准格式：{
 "code":0,
 "message":"",
 "data":{}
}
"""
import logging
import os

from dotenv import load_dotenv
from langgraph.graph import StateGraph, END

# 导入节点模块
from app.application.nodes.icrc_intent import icrc_parse_intent_and_entities
from app.application.nodes.icrc_nodes import icrc_extract_form_data
from app.core.executor import GenericToolExecutor
from app.domain.schemas import AssistantState, AssistantAppParams

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

FINAL_RESPONSE_PROMPT_TEMPLATE = """
你是一个智能助手，你需要根据用户原始问题和工具调用返回的结构化数据，生成一个友好、清晰、自然的中文回复。

用户原始问题:
{query}

工具调用成功，返回的结构化数据如下 (JSON格式):
```json
{tool_data}
```

该工具预期返回数据的 Schema 定义如下 (JSON Schema格式) (如果 Schema 不可用，则显示 'Schema not available.'):
```json
{tool_schema}
```
如果 Schema 可用，请参考 Schema 中字段的 'description' 来理解数据含义。

请根据上述信息，生成最终的回复。请只返回回复内容，不要包含其他前缀或解释。
"""

# Initialize shared executor once
config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", "tools_config.json")
try:
    shared_executor = GenericToolExecutor(config_path)
    logger.info(f"Shared GenericToolExecutor initialized from: {config_path}")
except Exception as e:
    logger.error(f"CRITICAL: Failed to initialize Shared GenericToolExecutor: {e}", exc_info=True)
    shared_executor = None

# 工作流缓存字典 - 用于存储已编译的LangGraph工作流
_workflow_cache = {}


def get_assistant_app(params: AssistantAppParams):
    """创建并编译特定于源的助手 LangGraph 工作流，使用缓存提高性能"""

    if shared_executor is None:
        raise RuntimeError("Shared GenericToolExecutor failed to initialize.")

    # 构建缓存键 - 基于source和sceneName(如果存在)
    # 安全地检查metaInfo及其嵌套属性
    if params.metaInfo is not None and hasattr(params.metaInfo, 'sceneInfo') and params.metaInfo.sceneInfo is not None:
        cache_key = params.metaInfo.sceneInfo.sceneName

    # 检查缓存中是否已有编译好的工作流
    if cache_key in _workflow_cache:
        logger.info(f"使用缓存的LangGraph工作流，缓存键: {cache_key}")
        return _workflow_cache[cache_key], shared_executor

    logger.info(f"创建新的LangGraph工作流，缓存键: {cache_key}")

    if params.metaInfo is not None and hasattr(params.metaInfo,
                                               'sceneInfo') and params.metaInfo.sceneInfo is not None and params.metaInfo.sceneInfo.sceneName == 'ICRC':

        # --- 构建 ICRC 图 --- #
        icrc_workflow = StateGraph(AssistantState)

        # 绑定 LLM 到节点函数
        # 使用 functools.partial 将 llm 实例绑定到节点函数，这样节点函数签名就只接受 state
        # 注意：LangGraph 现在推荐这种方式传递 LLM 或其他配置
        from functools import partial

        # 添加节点，使用 partial 绑定 LLM
        icrc_workflow.add_node("icrc_intent_parser", partial(icrc_parse_intent_and_entities))
        icrc_workflow.add_node("icrc_data_extractor", partial(icrc_extract_form_data))

        # 设置入口点
        icrc_workflow.set_entry_point("icrc_intent_parser")

        # 添加条件边
        def should_extract_data(state: AssistantState) -> str:
            """根据意图识别结果决定下一步走向"""
            if state.icrc_intent == "Fill_Form" or state.icrc_intent == "Refill_Form":
                # 把state.icrc_intent 打印出来
                logger.info(f"ICRC Router: Intent is '{state.icrc_intent}', routing to icrc_data_extractor.")
                return "icrc_data_extractor"
            else:
                # 如果意图是other，确保final_response字段被设置
                if state.icrc_intent == "other":
                    # 设置默认的引导消息
                    state.final_response = "当前为自动预填场景，请咨询相关问题，您可以问我\"请帮我从手术记录中重新提取手术原因和手术部位信息\"相关问题。"
                    logger.info(f"Setting default guidance message for 'other' intent: {state.final_response}")

                logger.info("ICRC Router: Intent is not 'Fill_Form', routing to end.")
                return END  # 如果意图不是 fill_form，直接结束

        # 从意图识别节点出发，根据 should_extract_data 的结果进行路由
        icrc_workflow.add_conditional_edges(
            "icrc_intent_parser",
            should_extract_data,
            {
                "icrc_data_extractor": "icrc_data_extractor",  # 如果返回 "icrc_data_extractor"，则去该节点
                END: END  # 如果返回 END，则结束
            }
        )

        # 从数据提取节点到结束
        icrc_workflow.add_edge("icrc_data_extractor", END)

        # 编译图
        icrc_app = icrc_workflow.compile()
        logger.info("ICRC LangGraph workflow compiled successfully.")

        # 缓存编译好的工作流
        _workflow_cache[cache_key] = icrc_app
        # 注意：ICRC 图现在不需要单独的 Executor，它使用 LangGraph 内置的执行器
        return icrc_app, None  # 返回 None 表示不使用旧的 Executor

    else:
        # logger.error(f"Unknown source provided: {params.source}")
        raise ValueError(f"Unknown or unsupported source is null")
