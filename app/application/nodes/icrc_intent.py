import datetime  # 导入 datetime 获取当前时间
import logging
import os
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

from app.utils.direct_llm import call_llm_api  # 导入统一的 API 调用工具
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate  # 导入 PromptTemplate
from pydantic import ValidationError  # 只导入 ValidationError

# 导入 Pydantic 模型
from app.domain.icrc_intent_entity import IntentOutput
from app.domain.schemas import AssistantState

logger = logging.getLogger(__name__)

# --- 数据模型定义 (Pydantic Schemas) ---
# 这些模型已移至 app.domain.icrc_intent_entity.py


logger = logging.getLogger(__name__)


# --- ICRC 意图识别与实体提取 ---

def load_prompt_from_file(file_path: Path) -> str:
    """从文件加载提示词模板"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        logger.error(f"Prompt file not found at: {file_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading prompt file {file_path}: {e}")
        raise


def extract_conversation_history(state: AssistantState) -> Tuple[str, str]:
    """
    从状态中提取对话历史，过滤掉知识上传消息，并将用户上传的知识存储到状态中。

    Args:
        state: 当前助手状态

    Returns:
        Tuple[str, str]: 对话历史字符串和最新用户消息
    """
    KNOWLEDGE_PREFIX = "附上用户上传的知识"
    conversation_history = ""
    latest_user_message = ""

    # 确保我们不会丢失之前可能已经存在的用户上传知识
    user_uploaded_knowledge = list(state.user_uploaded_knowledge) if state.user_uploaded_knowledge else []

    if not state.app_params or not state.app_params.questions:
        logger.warning("No questions found in state.app_params.")
        return "", ""

    for msg in state.app_params.questions:
        if not msg.content:
            continue

        if msg.content.strip().startswith(KNOWLEDGE_PREFIX):
            # 提取用户上传的知识
            logger.info("检测到用户上传的知识，将其提取并存储")
            knowledge_content = msg.content.strip()[len(KNOWLEDGE_PREFIX):].strip()
            # 创建一个KnowledgeFile对象，使用唯一标识作为fileId
            from app.domain.schemas import KnowledgeFile
            knowledge_file = KnowledgeFile(
                fileId=f"user_upload_{len(user_uploaded_knowledge)}",
                collectionName="user_upload",
                fileName="用户上传知识",
                fileType="text"
            )
            user_uploaded_knowledge.append(knowledge_file)

            # 将知识内容存储到状态中，以便后续使用
            state.user_uploaded_knowledge_content[knowledge_file.fileId] = knowledge_content
        else:
            # 正常对话消息
            role_prefix = "User" if msg.role == 'user' else "Assistant"
            conversation_history += f"{role_prefix}: {msg.content}\n"
            if msg.role == 'user':
                latest_user_message = msg.content

    # 将用户上传的知识存储到状态中，并确保它被正确传递
    state.user_uploaded_knowledge = user_uploaded_knowledge
    if user_uploaded_knowledge:
        logger.info(f"已提取并存储{len(user_uploaded_knowledge)}条用户上传的知识")
        # 打印详细信息以便调试
        for i, k_file in enumerate(user_uploaded_knowledge):
            logger.info(f"知识 {i+1}: fileId={k_file.fileId}, fileName={k_file.fileName}")
            content_preview = state.user_uploaded_knowledge_content.get(k_file.fileId, "")[:50]
            logger.info(f"内容预览: {content_preview}...")

    return conversation_history.strip(), latest_user_message


def create_intent_prompt(conversation_history: str) -> Tuple[PromptTemplate, str]:
    """
    创建意图识别的提示词模板。

    Args:
        conversation_history: 过滤后的对话历史

    Returns:
        Tuple[PromptTemplate, str]: 提示词模板和当前时间字符串
    """
    # 加载提示词模板
    prompt_file_path = Path(__file__).parent / "icrc_parse_intent_and_entities.md"
    try:
        prompt_template_str = load_prompt_from_file(prompt_file_path)
    except Exception:
        logger.exception("Failed to load prompt template.")  # 记录完整错误栈
        raise

    # 创建 PromptTemplate 实例
    prompt = PromptTemplate(
        template=prompt_template_str,
        input_variables=["conversation_history", "current_time"],
        template_format="jinja2"  # 明确指定 Jinja2 格式
    )

    # 获取当前时间
    current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    return prompt, current_time_str


def call_llm_for_intent(prompt: PromptTemplate, conversation_history: str, current_time_str: str,
                        state: AssistantState) -> Tuple[str, Optional[str]]:
    """
    调用 LLM API 获取意图识别结果。

    Args:
        prompt: 提示词模板
        conversation_history: 对话历史
        current_time_str: 当前时间字符串
        state: 当前助手状态

    Returns:
        Tuple[str, Optional[str]]: LLM 响应内容和思考过程
    """
    # 生成提示词
    prompt_value = prompt.invoke({
        "conversation_history": conversation_history,
        "current_time": current_time_str
    })
    logger.debug("Generated prompt value.")

    # 调用 LLM API
    provider = os.getenv("LLM_PROVIDER", "deepseek")  # 从环境变量获取 provider，默认为 deepseek
    logger.info(f"Calling {provider} API directly")

    # 将 PromptValue 对象转换为文本
    prompt_text = prompt_value.to_string()
    logger.info(f"Rendered prompt text: {prompt_text[:100]}...")

    stream = False
    if state.app_params.chatType == "stream":
        stream = True
        logger.info("Stream mode enabled.")

    content, thinking_process = call_llm_api(
        prompt=prompt_text,
        provider=provider,
        model=os.getenv("FLASH_LLM_MODEL_NAME"),
        temperature=float(os.getenv("LLM_TEMPERATURE", "0")),
        is_intent_recognition=True  # 标记为意图识别阶段，不发送answering事件
    )

    # 处理思考过程
    if thinking_process and isinstance(thinking_process, str):
        logger.info(f"Found thinking process from direct API call.")
        if not hasattr(state, 'thought_log') or state.thought_log is None:
            state.thought_log = []
        state.thought_log.append(thinking_process)
        logger.info("Added thinking process to state.thought_log.")
    else:
        logger.warning("No thinking process found in API response.")

    return content, thinking_process


def parse_llm_response(content: str, parser: JsonOutputParser) -> Optional[Dict[str, Any]]:
    """
    解析 LLM 响应内容。

    Args:
        content: LLM 响应内容
        parser: JSON 输出解析器

    Returns:
        Optional[Dict[str, Any]]: 解析后的结果字典
    """
    if not content or not isinstance(content, str):
        logger.error("API response missing content or content is not a string.")
        return None

    try:
        result = parser.parse(content)
        logger.info(f"Parsed LLM Output: {result}")
        return result
    except Exception as parse_error:
        logger.error(f"Failed to parse LLM content. Error: {parse_error}", exc_info=True)
        logger.debug(f"LLM Content that failed parsing: {content}")
        return None


def _validate_intent_output(result_dict: Dict[str, Any]) -> Optional[IntentOutput]:
    """验证解析后的LLM输出字典是否符合 IntentOutput 模型。

    Args:
        result_dict: 从 LLM 响应解析得到的字典。

    Returns:
        如果验证通过，返回 IntentOutput Pydantic 对象，否则返回 None。
    """
    try:
        # 使用 IntentOutput 模型进行验证和转换
        validated_output = IntentOutput.model_validate(result_dict)
        logger.info(f"Validated LLM Output: {validated_output}")
        return validated_output
    except ValidationError as e:
        logger.error(f"LLM output validation failed: {e}")
        logger.debug(f"Data that failed validation: {result_dict}")
        return None
    except Exception as e:  # 捕获其他可能的错误
        logger.error(f"An unexpected error occurred during validation: {e}", exc_info=True)
        return None


def handle_intent_result(result: Optional[Dict[str, Any]], state: AssistantState) -> Dict[str, Any]:
    """处理意图识别和实体提取的最终结果，并更新状态字典。

    Args:
        result: 从 LLM 响应解析得到的字典 (可能为 None)。
        state: 当前助手状态。

    Returns:
        更新后的状态部分字典。
    """
    # 1. 验证解析结果
    validated_result = _validate_intent_output(result) if result else None

    # 2. 根据验证结果更新状态
    if validated_result:
        intent = validated_result.intent
        source = validated_result.source  # 可能为 None
        details = validated_result.details  # FillFormDetails 对象或 None

        logger.info(f"Identified Intent: {intent}, Source: {source}, Details: {details}")

        # 准备要更新到 LangGraph state 的字典
        update_dict = {
            "icrc_intent": intent,
            "icrc_source": source,  # source 直接存入
            # 将 Pydantic 对象转换为字典以便存入 LangGraph 状态
            "icrc_extracted_details": details,
            "thought_log": getattr(state, 'thought_log', [])  # 保留思考过程日志
        }

        # 如果details中有table_code，则将其提取出来并添加到状态中
        if details and hasattr(details, 'table_code') and details.table_code:
            update_dict["table_code"] = details.table_code
            logger.info(f"从details中提取table_code: {details.table_code}")
        else:
            logger.info("details中没有table_code或table_code为空")
        return update_dict

    else:
        # 如果解析或验证失败，将意图设置为 'other'，并清空详情
        logger.warning("LLM output parsing or validation failed. Falling back to 'other' intent.")
        # 添加引导消息作为final_response
        guidance_message = "当前为自动预填场景，请咨询相关问题，您可以问我\"请帮我从手术记录中重新提取手术原因和手术部位信息\"相关问题。"
        logger.info(f"Adding guidance message for 'other' intent: {guidance_message}")
        return {
            "icrc_intent": "other",
            "icrc_source": None,
            "icrc_extracted_details": None,
            "thought_log": getattr(state, 'thought_log', []),
            "table_code": None,  # 添加table_code字段，设置为None
            "final_response": guidance_message  # 添加引导消息作为最终响应
        }


# 节点入口
def icrc_parse_intent_and_entities(state: AssistantState) -> Dict[str, Any]:
    """
    LangGraph 节点：识别 ICRC 场景下的用户意图、提取实体和判断数据来源。

    通过调用 LLM 分析对话历史和用户最新消息，判断意图是 Fill_Form,
    Refill_Form 还是 other。如果是 Refill_Form，则进一步判断 source。
    提取出的详细信息（如表单名、受试者ID、时间）会存入状态。

    Args:
        state: 当前助手状态 (AssistantState Pydantic 模型)。

    Returns:
        一个字典，包含需要更新到 LangGraph 状态的键值对，
        主要包括 icrc_intent, icrc_source, icrc_extracted_details。
    """
    logger.info("--- [Node Start] icrc_parse_intent_and_entities ---")

    # 1. 提取对话历史
    logger.info("Step 1: Extracting conversation history")
    conversation_history, latest_user_message = extract_conversation_history(state)
    if not latest_user_message:
        logger.warning("No user message found, returning 'other' intent with guidance message.")
        guidance_message = "当前为自动预填场景，请咨询相关问题，您可以问我\"请帮我从手术记录中重新提取手术原因和手术部位信息\"相关问题。"
        logger.info(f"Adding guidance message for 'other' intent (no user message): {guidance_message}")
        logger.info("--- [Node End] icrc_parse_intent_and_entities ---")
        return {
            "icrc_intent": "other",
            "icrc_source": None,
            "icrc_extracted_details": None,
            "thought_log": getattr(state, 'thought_log', []),
            "final_response": guidance_message  # 添加引导消息作为最终响应
        }
    logger.info(f"Latest user message: {latest_user_message}")
    logger.debug(f"Conversation history: {conversation_history}")

    # 检查用户上传的知识是否已正确存储
    if hasattr(state, 'user_uploaded_knowledge') and state.user_uploaded_knowledge:
        logger.info(f"用户上传知识已存储: {len(state.user_uploaded_knowledge)}条")
        for i, k_file in enumerate(state.user_uploaded_knowledge):
            logger.info(f"知识 {i+1}: fileId={k_file.fileId}, fileName={k_file.fileName}")
            if hasattr(state, 'user_uploaded_knowledge_content') and k_file.fileId in state.user_uploaded_knowledge_content:
                content_preview = state.user_uploaded_knowledge_content[k_file.fileId][:50]
                logger.info(f"内容预览: {content_preview}...")
            else:
                logger.warning(f"未找到知识内容: fileId={k_file.fileId}")
    else:
        logger.info("未检测到用户上传的知识")

    # 2. 创建提示词
    logger.info("Step 2: Creating intent prompt")
    try:
        prompt, current_time_str = create_intent_prompt(conversation_history)
    except Exception:
        logger.exception("Failed to create intent prompt. Returning 'other' intent with guidance message.")
        guidance_message = "当前为自动预填场景，请咨询相关问题，您可以问我\"请帮我从手术记录中重新提取手术原因和手术部位信息\"相关问题。"
        logger.info(f"Adding guidance message for 'other' intent (prompt creation failed): {guidance_message}")
        logger.info("--- [Node End] icrc_parse_intent_and_entities ---")
        return {
            "icrc_intent": "other",
            "icrc_source": None,
            "icrc_extracted_details": None,
            "thought_log": getattr(state, 'thought_log', []),
            "final_response": guidance_message  # 添加引导消息作为最终响应
        }

    # 3. 调用 LLM
    logger.info("Step 3: Calling LLM for intent")
    try:
        content, thinking_process = call_llm_for_intent(prompt, conversation_history, current_time_str, state)
        logger.info(f"LLM call successful. Received content length: {len(content) if content else 0}")
        logger.debug(f"LLM Raw Content: {content}")
        logger.debug(f"LLM Thinking Process: {thinking_process}")
    except Exception as e:
        logger.error(f"Error calling LLM for intent: {e}", exc_info=True)
        guidance_message = "当前为自动预填场景，请咨询相关问题，您可以问我\"请帮我从手术记录中重新提取手术原因和手术部位信息\"相关问题。"
        logger.info(f"Adding guidance message for 'other' intent (LLM call failed): {guidance_message}")
        logger.info("--- [Node End] icrc_parse_intent_and_entities ---")
        return {
            "icrc_intent": "other",
            "icrc_source": None,
            "icrc_extracted_details": None,
            "thought_log": getattr(state, 'thought_log', []),
            "final_response": guidance_message,  # 添加引导消息作为最终响应
            "error": f"LLM call failed: {e}"
        }

    # 4. 解析 LLM 响应
    logger.info("Step 4: Parsing LLM response")
    parser = JsonOutputParser()
    result_dict = parse_llm_response(content, parser)
    if result_dict is None or result_dict.get("intent") == 'other':
        logger.warning("Parsing LLM response failed or returned None.")
        guidance_message = "当前为自动预填场景，请咨询相关问题，您可以问我\"请帮我从手术记录中重新提取手术原因和手术部位信息\"相关问题。"
        logger.info(f"Adding guidance message for 'other' intent (parsing failed): {guidance_message}")
        logger.info("--- [Node End] icrc_parse_intent_and_entities ---")
        return {
            "icrc_intent": "other",
            "icrc_source": None,
            "icrc_extracted_details": None,
            "thought_log": getattr(state, 'thought_log', []),
            "final_response": guidance_message,  # 添加引导消息作为最终响应
            "error": "Failed to parse LLM response"
        }
    logger.info("LLM response parsed successfully.")
    logger.debug(f"Parsed result dictionary: {result_dict}")

    # 5. 验证解析结果
    logger.info("Step 5: Validating parsed output")
    validated_output = _validate_intent_output(result_dict)
    if validated_output is None:
        logger.warning("Validation of parsed output failed or returned None.")
        guidance_message = "当前为自动预填场景，请咨询相关问题，您可以问我\"请帮我从手术记录中重新提取手术原因和手术部位信息\"相关问题。"
        logger.info(f"Adding guidance message for 'other' intent (validation failed): {guidance_message}")
        logger.info("--- [Node End] icrc_parse_intent_and_entities ---")
        return {
            "icrc_intent": "other",
            "icrc_source": None,
            "icrc_extracted_details": None,
            "thought_log": getattr(state, 'thought_log', []),
            "final_response": guidance_message,  # 添加引导消息作为最终响应
            "error": "LLM output validation failed"
        }
    logger.info("Parsed output validated successfully.")
    logger.debug(f"Validated output object: {validated_output}")

    # 6. 处理意图结果并更新状态
    logger.info("Step 6: Handling intent result and preparing state update")
    update_dict = handle_intent_result(validated_output, state)

    # 添加思考过程到最终更新字典中 (如果存在)
    if thinking_process:
        current_thought_log = state.thought_log or []
        if not current_thought_log or current_thought_log[-1] != thinking_process:
             update_dict['thought_log'] = current_thought_log + [thinking_process]
        else:
            update_dict['thought_log'] = current_thought_log
        logger.info("Thinking process added to update_dict.")
    else:
        update_dict['thought_log'] = state.thought_log or []

    # 确保用户上传的知识被包含在返回的状态更新中
    if hasattr(state, 'user_uploaded_knowledge') and state.user_uploaded_knowledge:
        update_dict['user_uploaded_knowledge'] = state.user_uploaded_knowledge
        update_dict['user_uploaded_knowledge_content'] = state.user_uploaded_knowledge_content
        logger.info(f"用户上传知识已添加到状态更新中: {len(state.user_uploaded_knowledge)}条")

    logger.info(f"Prepared state update: {update_dict}")
    logger.info("--- [Node End] icrc_parse_intent_and_entities ---")
    return update_dict

# --- LangGraph 节点注册 ---
# 确保在你的 LangGraph 定义中，节点引用的是这个新的函数名: icrc_parse_intent_and_entities
# 例如:
# workflow.add_node("parse_intent", icrc_parse_intent_and_entities)
