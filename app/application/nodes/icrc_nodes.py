import copy
import importlib
import logging
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Callable
from functools import wraps

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate

from app.domain.schemas import AssistantState, DataSource, CrfTable
from app.function_call import structure,paper
from app.models.schemas import QueryRequest, KnowledgeSlice
from app.services.query_service import QueryService
from app.utils.direct_llm import call_llm_api  # 导入统一的 API 调用工具
from app.utils.text_locator import find_start_position
from app.utils.visit_formatter import format_visits_data  # 导入visits数据格式化工具

logger = logging.getLogger(__name__)

query_service = QueryService()

# 添加trace和耗时统计的装饰器
def trace_time(func_name: str = None):
    """
    装饰器：记录函数执行时间和添加trace日志

    Args:
        func_name: 函数名称，如果为None则使用被装饰函数的名称
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数名
            name = func_name or func.__name__
            # 记录开始时间
            start_time = time.time()
            # 记录开始trace
            logger.info(f"TRACE - 开始执行: {name}")

            try:
                # 执行原函数
                result = func(*args, **kwargs)
                # 计算耗时
                elapsed_time = time.time() - start_time
                # 记录结束trace和耗时
                logger.info(f"TRACE - 结束执行: {name} - 耗时: {elapsed_time:.4f}秒")
                return result
            except Exception as e:
                # 计算耗时
                elapsed_time = time.time() - start_time
                # 记录异常trace和耗时
                logger.error(f"TRACE - 执行异常: {name} - 耗时: {elapsed_time:.4f}秒 - 错误: {str(e)}")
                raise

        return wrapper

    return decorator


# --- 校验函数 ---
@trace_time("校验app_params")
def validate_app_params(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验app_params是否存在"""
    app_params = state.app_params
    if not app_params:
        logger.warning("ICRC Extractor: 'app_params' missing in state.")
        return {"icrc_form_data": None, "final_response": "Request state is missing required parameters."}
    return None


@trace_time("校验metaInfo")
def validate_meta_info(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验metaInfo是否存在"""
    meta_info = state.app_params.metaInfo
    if not meta_info:
        logger.warning("ICRC Extractor: No 'metaInfo' found in app_params.")
        return {"icrc_form_data": None, "final_response": "Request is missing metadata."}
    return None


@trace_time("校验crfInfo")
def validate_crf_info(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验crfInfo和tableList是否存在"""
    crf_info = state.app_params.metaInfo.crfInfo
    if not crf_info or not state.app_params.metaInfo.crfInfo.tableList:
        logger.warning("ICRC Extractor: No 'crfInfo' or 'tableList' found.")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "No form definition found to fill."}
    return None


@trace_time("校验visits")
def validate_visits(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验visits是否存在"""
    visits = state.app_params.metaInfo.visits
    if not visits:
        logger.warning("ICRC Extractor: No 'visits' data found for extraction.")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "找不到对应的访视."}
    return None


@trace_time("校验tableList")
def validate_table_list(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验tableList是否为空"""
    if not state.app_params.metaInfo.crfInfo.tableList:
        logger.warning("ICRC Extractor: 'tableList' is empty.")
        return {"icrc_form_data": [], "final_response": "Form definition list is empty."}
    return None


@trace_time("校验crfIndexList")
def validate_crf_index_list(state: AssistantState, target_table) -> Optional[Dict[str, Any]]:
    """校验crfIndexList是否存在"""
    target_indices = target_table.crfIndexList
    if not target_indices:
        logger.warning(f"ICRC Extractor: No 'crfIndexList' in first table ('{target_table.crfTableName}').")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "First form definition is missing fields to fill."}
    return None


@trace_time("校验target_field_names")
def validate_target_field_names(target_indices) -> Tuple[Optional[Dict[str, Any]], List[str]]:
    """校验target_field_names是否有效，并返回有效的字段名列表"""
    target_field_names = [index.crfIndexName for index in target_indices if hasattr(index, 'crfIndexName')]
    if not target_field_names:
        logger.warning("ICRC Extractor: No valid crfIndexName found in target_indices.")
        return {"icrc_form_data": None, "final_response": "No target field names defined in the form."}, []
    return None, target_field_names


@trace_time("校验visit数据")
def validate_visit_data(state: AssistantState) -> Optional[Dict[str, Any]]:
    """校验visit数据是否有效"""
    if not any(visit.data for visit in state.app_params.metaInfo.visits):
        logger.warning("ICRC Extractor: No valid data found in visits.")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "No valid data found in visit records to extract from."}
    return None


@trace_time("生成目标字段schema")
def generate_target_fields_schema(target_indices) -> Tuple[Dict[str, str], str]:
    """
    根据目标索引生成字段schema及其格式化字符串

    Args:
        target_indices: CRF索引列表

    Returns:
        Tuple[Dict[str, str], str]: 包含字段schema字典和格式化后的字符串
    """
    # 生成目标字段schema
    target_fields_schema = {}
    for index_item in target_indices:
        if hasattr(index_item, 'crfIndexName') and hasattr(index_item, 'crfIndexType'):
            target_fields_schema[index_item.crfIndexName] = index_item.crfIndexType

    # 将schema转换为格式化的字符串，避免JSON格式与LangChain模板变量语法冲突
    target_fields_schema_str = ""
    for field_name, field_type in target_fields_schema.items():
        target_fields_schema_str += f'"{field_name}": "{field_type}"\n'

    return target_fields_schema, target_fields_schema_str


@trace_time("处理记录内容")
def process_record_contents(records) -> Tuple[Dict[str, str], List[str], Dict[str, str]]:
    """
    处理记录内容，提取有效内容并映射记录ID

    Args:
        records: 记录列表

    Returns:
        Tuple[Dict[str, str], List[str], Dict[str, str]]:
            包含记录内容映射、合并内容部分和全局记录内容映射
    """
    record_contents_map = {}
    combined_content_parts = []
    global_record_content_map = {}

    for record in records:
        content = record.content
        record_id = record.recordId
        global_record_content_map[record_id] = content
        if content and isinstance(content, str) and content.strip():
            clean_content = content.strip()
            record_contents_map[record_id] = clean_content
            combined_content_parts.append(clean_content)

    return record_contents_map, combined_content_parts, global_record_content_map


@trace_time("调用LLM进行信息提取")
def call_llm_for_extraction(prompt_text: str, is_repeat: int, state: AssistantState) -> Tuple[
    List[Dict[str, Any]], bool]:
    """
    调用LLM进行信息提取

    Args:
        prompt_text: 提示词文本
        is_repeat: 是否为循环表单
        state: 当前状态

    Returns:
        Tuple[List[Dict[str, Any]], bool]: 提取结果列表和是否成功标志
    """
    try:
        # 记录开始时间
        start_time = time.time()

        # 调用LLM API
        provider = os.getenv("LLM_PROVIDER", "deepseek")  # 从环境变量获取 provider，默认为 deepseek
        logger.info(f"ICRC Extractor: Calling {provider} API directly")
        stream = False
        if state.app_params.chatType == "stream":
            stream = True
            logger.info("Stream mode enabled.")

        # 调用API
        logger.info(f"TRACE - 开始调用LLM API")
        api_start_time = time.time()
        content, thinking_process = call_llm_api(
            prompt=prompt_text,
            provider=provider,
            model=os.getenv("LLM_MODEL_NAME"),
            temperature=float(os.getenv("LLM_TEMPERATURE", "0")),
            is_form_extraction=True  # 标记为表单信息提取阶段，不发送answering事件
        )
        api_elapsed_time = time.time() - api_start_time
        logger.info(f"TRACE - 结束调用LLM API - 耗时: {api_elapsed_time:.4f}秒")

        # 处理思考过程
        logger.info(f"TRACE - 开始处理思考过程")
        thinking_start_time = time.time()
        if thinking_process and isinstance(thinking_process, str):
            logger.info(f"ICRC Extractor: Found thinking process from direct API call.")
            if not hasattr(state, 'thought_log') or state.thought_log is None:
                state.thought_log = []
            state.thought_log.append(thinking_process)
            logger.info("ICRC Extractor: Added thinking process to state.thought_log.")
        else:
            logger.warning("ICRC Extractor: No thinking process found in API response.")
        thinking_elapsed_time = time.time() - thinking_start_time
        logger.info(f"TRACE - 结束处理思考过程 - 耗时: {thinking_elapsed_time:.4f}秒")

        # 设置输出解析器
        logger.info(f"TRACE - 开始设置输出解析器")
        parser_start_time = time.time()
        if is_repeat == 1:
            output_parser = JsonOutputParser(pydantic_object=List[Dict[str, Any]])
            logger.info("ICRC Extractor: Expecting a JSON list from LLM (isRepeat=1)")
        else:
            output_parser = JsonOutputParser(pydantic_object=Dict[str, Any])
            logger.info("ICRC Extractor: Expecting a JSON object from LLM (isRepeat=0)")
        parser_elapsed_time = time.time() - parser_start_time
        logger.info(f"TRACE - 结束设置输出解析器 - 耗时: {parser_elapsed_time:.4f}秒")

        # 解析内容
        logger.info(f"TRACE - 开始解析LLM输出内容")
        parse_start_time = time.time()
        if content and isinstance(content, str):
            llm_output = output_parser.parse(content)
            logger.info(f"ICRC Extractor: Parsed LLM Output type: {type(llm_output)}")
        else:
            logger.error("ICRC Extractor: API response missing content or content is not a string")
            llm_output = [] if is_repeat == 1 else {}
        parse_elapsed_time = time.time() - parse_start_time
        logger.info(f"TRACE - 结束解析LLM输出内容 - 耗时: {parse_elapsed_time:.4f}秒")

        # 确保输出始终是列表
        logger.info(f"TRACE - 开始处理输出格式")
        format_start_time = time.time()
        if isinstance(llm_output, dict):
            extracted_results = [llm_output]
        elif isinstance(llm_output, list):
            extracted_results = llm_output
        else:
            logger.warning(
                f"ICRC Extractor: Unexpected LLM output type: {type(llm_output)}. Skipping results.")
            extracted_results = []
        format_elapsed_time = time.time() - format_start_time
        logger.info(f"TRACE - 结束处理输出格式 - 耗时: {format_elapsed_time:.4f}秒")

        # 计算总耗时
        total_elapsed_time = time.time() - start_time
        logger.info(f"TRACE - LLM调用及处理总耗时: {total_elapsed_time:.4f}秒")

        return extracted_results, True

    except Exception as e:
        logger.error(f"ICRC Extractor: LLM chain invocation failed. Error: {e}")
        return [], False


@trace_time("处理提取结果")
def process_extraction_results(extracted_results: List[Dict[str, Any]], record_contents_map: Dict[str, str],
                               visit_id: str) -> List[Dict[str, Any]]:
    """
    处理提取结果并映射记录ID

    Args:
        extracted_results: 提取结果列表
        record_contents_map: 记录内容映射
        visit_id: 访问ID

    Returns:
        List[Dict[str, Any]]: 处理后的结果列表
    """
    processed_results = []

    for result_item in extracted_results:
        found_record_id = None
        hit_value_field = None
        hit_value = None

        # 查找第一个匹配的hit_value
        logger.info(f"TRACE - 开始查找匹配的hit_value")
        hit_value_start_time = time.time()
        for key, value in result_item.items():
            if key.endswith("_hit_value") and isinstance(value, str) and value.strip():
                hit_value_field = key
                hit_value_content = value.strip()

                # 在原始记录内容中搜索hit_value
                for rec_id, rec_content in record_contents_map.items():
                    if hit_value_content in rec_content:
                        found_record_id = rec_id
                        logger.info(
                            f"ICRC Extractor: Mapped result item using '{hit_value_field}' to record_id: {found_record_id}")
                        break

                if found_record_id:
                    hit_value = hit_value_content
                    break
        hit_value_elapsed_time = time.time() - hit_value_start_time
        logger.info(f"TRACE - 结束查找匹配的hit_value - 耗时: {hit_value_elapsed_time:.4f}秒")

        # 如果找不到记录ID，使用回退策略
        if not found_record_id:
            logger.info(f"TRACE - 开始使用回退策略查找记录ID")
            fallback_start_time = time.time()
            if record_contents_map:
                found_record_id = next(iter(record_contents_map))
                logger.warning(
                    f"ICRC Extractor: Could not map result item using any _hit_value. Associating with first record_id: {found_record_id}")
            else:
                logger.warning("ICRC Extractor: Could not map result item and no record_ids available.")
                continue
            fallback_elapsed_time = time.time() - fallback_start_time
            logger.info(f"TRACE - 结束使用回退策略查找记录ID - 耗时: {fallback_elapsed_time:.4f}秒")

        # 添加记录ID和访问ID到结果项
        result_item['__found_record_id__'] = found_record_id
        result_item['__visit_id__'] = visit_id
        if hit_value_field:
            result_item[hit_value_field] = hit_value
        processed_results.append(result_item)

    return processed_results


# --- ICRC 表单信息提取 ---
@trace_time("ICRC表单信息提取")
def icrc_extract_form_data(state: AssistantState) -> Dict[str, Any]:  # llm is now required
    """
    Extracts data from state.app_params.metaInfo.visits text content
    to fill the form defined in state.app_params.metaInfo.crfInfo.
    Uses LLM for structured extraction based on crfIndexNames.

    如果意图是Refill_Form，则调用handle_refill_form函数处理。
    """
    logger.info("--- Executing ICRC Form Data Extractor Node (LLM-Based) ---")
    # 打印 state.icrc_extracted_details
    if state.icrc_extracted_details:
        logger.info(f"ICRC Extractor: Extracted details: {state.icrc_extracted_details}")

    # 检查意图是否为Refill_Form
    if hasattr(state, 'icrc_intent') and state.icrc_intent == "Refill_Form":
        logger.info("TRACE - 开始处理Refill_Form意图")
        refill_start_time = time.time()
        logger.info("ICRC Extractor: 检测到Refill_Form意图，调用handle_refill_form处理")
        # 调用handle_refill_form获取表单数据
        refill_result = handle_refill_form(state)

        # 检查是否成功获取到数据
        if refill_result.get("crf_result"):
            logger.info("ICRC Extractor: Refill_Form处理成功获取数据，将数据设置到state中并继续处理")

            # 如果有visits数据，也更新到state中
            if refill_result.get("visits"):
                # 使用工具函数格式化visits数据
                visits_data = refill_result.get("visits")
                try:
                    # 调用工具函数格式化visits数据
                    logger.info("TRACE - 开始格式化visits数据")
                    format_start_time = time.time()
                    visit_objects = format_visits_data(visits_data)
                    format_elapsed_time = time.time() - format_start_time
                    logger.info(f"TRACE - 结束格式化visits数据 - 耗时: {format_elapsed_time:.4f}秒")

                    if visit_objects:
                        # 更新state中的visits数据
                        state.app_params.metaInfo.visits = visit_objects
                        logger.info(
                            f"ICRC Extractor: 已将Refill_Form获取的{len(visit_objects)}个visits数据设置到state中")
                    else:
                        logger.warning("格式化visits数据失败，保留原始visits数据")
                except Exception as e:
                    logger.error(f"处理visits数据时出错: {str(e)}")
                    # 如果处理失败，保留原始visits数据
                    logger.warning("处理visits数据出错，保留原始visits数据")
            else:
                logger.warning("ICRC Extractor: Refill_Form处理未获取到有效的表单数据，返回原始结果")
        else:
            state.app_params.metaInfo.visits = []

        refill_elapsed_time = time.time() - refill_start_time
        logger.info(f"TRACE - 结束处理Refill_Form意图 - 耗时: {refill_elapsed_time:.4f}秒")

    # --- 1. 校验输入数据 ---
    logger.info("TRACE - 开始校验输入数据")
    validation_start_time = time.time()

    validation_result = validate_app_params(state)
    if validation_result:
        return validation_result

    validation_result = validate_meta_info(state)
    if validation_result:
        return validation_result

    validation_result = validate_crf_info(state)
    if validation_result:
        return validation_result

    validation_result = validate_visits(state)
    if validation_result:
        return validation_result

    validation_result = validate_table_list(state)
    if validation_result:
        return validation_result

    validation_elapsed_time = time.time() - validation_start_time
    logger.info(f"TRACE - 结束校验输入数据 - 耗时: {validation_elapsed_time:.4f}秒")

    # --- 2. 获取目标字段和准备上下文 ---
    logger.info("TRACE - 开始获取目标字段和准备上下文")
    prepare_start_time = time.time()

    crf_info = state.app_params.metaInfo.crfInfo
    visits = state.app_params.metaInfo.visits
    knowledge = state.app_params.metaInfo.knowledge

    target_table = state.app_params.metaInfo.crfInfo.tableList[0]  # Process first table

    validation_result = validate_crf_index_list(state, target_table)
    if validation_result:
        return validation_result

    target_indices = target_table.crfIndexList
    validation_result, target_field_names = validate_target_field_names(target_indices)
    if validation_result:
        return validation_result

    logger.info(f"ICRC Extractor: Target fields for LLM extraction: {target_field_names}")

    validation_result = validate_visit_data(state)
    if validation_result:
        return validation_result

    prepare_elapsed_time = time.time() - prepare_start_time
    logger.info(f"TRACE - 结束获取目标字段和准备上下文 - 耗时: {prepare_elapsed_time:.4f}秒")

    # --- 3. Define LLM Prompt for Extraction ---
    logger.info("TRACE - 开始准备LLM提示词")
    prompt_start_time = time.time()

    # Create a string list of target fields for the prompt
    target_fields_str = "\n".join([f"- {name}" for name in target_field_names])

    # 生成目标字段schema并转换为格式化字符串
    target_fields_schema, target_fields_schema_str = generate_target_fields_schema(target_indices)

    logger.info(f"ICRC Extractor: Generated target fields schema: {target_fields_schema}")

    # Load the base template from the external .md file
    try:
        logger.info("TRACE - 开始加载提示词模板")
        template_start_time = time.time()
        prompt_file_path = Path(__file__).parent / "icrc_extract_form_data.md"
        base_prompt_template = prompt_file_path.read_text(encoding='utf-8')
        template_elapsed_time = time.time() - template_start_time
        logger.info(f"TRACE - 结束加载提示词模板 - 耗时: {template_elapsed_time:.4f}秒")
    except FileNotFoundError:
        logger.error("ICRC Extractor: Prompt template file 'icrc_extract_form_data.md' not found.")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "Internal error: Prompt template file missing."}
    except Exception as e:
        logger.error(f"ICRC Extractor: Error reading prompt template file 'icrc_extract_form_data.md': {e}")
        return {"icrc_form_data": state.app_params.metaInfo.crfInfo.tableList,
                "final_response": "Internal error: Could not read prompt template."}

    # --- 3.5 检索知识库增强提示词 ---
    logger.info("TRACE - 开始检索知识库增强提示词")
    knowledge_start_time = time.time()

    # 检查用户上传的知识
    if state.user_uploaded_knowledge:
        logger.info(f"ICRC Extractor: 检测到{len(state.user_uploaded_knowledge)}条用户上传的知识")
        for i, k_file in enumerate(state.user_uploaded_knowledge):
            logger.info(f"用户知识 {i+1}: fileId={k_file.fileId}, fileName={k_file.fileName}")
            if k_file.fileId in state.user_uploaded_knowledge_content:
                content_preview = state.user_uploaded_knowledge_content[k_file.fileId][:50]
                logger.info(f"内容预览: {content_preview}...")
            else:
                logger.warning(f"未找到知识内容: fileId={k_file.fileId}")
    else:
        logger.info("ICRC Extractor: 未检测到用户上传的知识")

    # 为每个字段单独检索知识库，同时传入state以处理用户上传的知识
    knowledge_context, knowledge_slices = recallKnowledge(knowledge, target_field_names, state)

    # 将知识切片添加到state中
    state.knowledge_slice = knowledge_slices
    logger.info(f"ICRC Extractor: Added {len(knowledge_slices)} knowledge slices to state")

    knowledge_elapsed_time = time.time() - knowledge_start_time
    logger.info(f"TRACE - 结束检索知识库增强提示词 - 耗时: {knowledge_elapsed_time:.4f}秒")

    # Define the prompt template ONCE using the processed template string
    logger.info("TRACE - 开始创建提示词模板")
    template_create_start_time = time.time()
    prompt_template = PromptTemplate(
        template=base_prompt_template,
        input_variables=["knowledge_context", "target_fields_list_placeholder", "target_fields_schema", "source_text"],
        template_format="jinja2"
    )
    template_create_elapsed_time = time.time() - template_create_start_time
    logger.info(f"TRACE - 结束创建提示词模板 - 耗时: {template_create_elapsed_time:.4f}秒")

    prompt_elapsed_time = time.time() - prompt_start_time
    logger.info(f"TRACE - 结束准备LLM提示词 - 耗时: {prompt_elapsed_time:.4f}秒")

    # --- 4. 为每个visit独立处理并提取数据 ---
    logger.info("TRACE - 开始处理visit数据")
    visit_process_start_time = time.time()

    visit_results = []
    visit_count = 0
    successful_visits = 0

    # 获取isRepeat参数，判断是否为循环表单
    is_repeat = state.app_params.metaInfo.isRepeat
    logger.info(f"ICRC Extractor: Form isRepeat = {is_repeat}")
    recordContentMap = {}

    # 遍历每个visit
    for visit in state.app_params.metaInfo.visits:
        visit_id = visit.visitId
        logger.info(f"TRACE - 开始处理visit: {visit_id}")
        single_visit_start_time = time.time()

        visit_data = visit.data
        # Store results associated with this visit
        current_visit_results = []
        visit_processed = False
        found_complete_form = False  # For non-repeat forms

        for data_item in visit_data:
            logger.info(f"TRACE - 开始处理data_item")
            data_item_start_time = time.time()

            records = data_item.records
            # 处理记录内容
            record_contents_map, combined_content_parts, record_content_updates = process_record_contents(records)
            # 更新全局记录内容映射
            recordContentMap.update(record_content_updates)

            # Skip if no valid content in this data_item
            if not combined_content_parts:
                logger.info("TRACE - 跳过无有效内容的data_item")
                continue

            # Combine content for LLM input
            # Using newline as a separator, consider if another separator is better
            source_text = "\n\n".join(combined_content_parts)

            # If non-repeat and already found complete data, skip further processing for this visit
            # Note: This logic might need adjustment depending on how 'completeness' is defined with combined text
            if is_repeat == 0 and found_complete_form:
                logger.info(
                    f"ICRC Extractor: Non-repeat form with complete data found for visit {visit_id}, skipping remaining data_items")
                break  # Break data_item loop for this visit

            visit_count += 1  # Increment count per data_item processed
            logger.info(
                f"ICRC Extractor: Processing visit {visit_id}, combined content from {len(record_contents_map)} records.")
            # logger.debug(f"Combined source text: {source_text[:500]}...") # Optional: log partial text

            # --- Prepare and Invoke LLM for the combined content ---
            logger.info("TRACE - 开始准备LLM输入")
            llm_prep_start_time = time.time()

            # Prepare the input dictionary for the prompt template
            if not knowledge_context:
                current_knowledge_context = "未提供"
            else:
                current_knowledge_context = knowledge_context

            prompt_input = {
                "target_fields_list_placeholder": target_fields_str,
                "source_text": source_text,
                "target_fields_schema": target_fields_schema_str,
                "knowledge_context": current_knowledge_context
            }

            llm_prep_elapsed_time = time.time() - llm_prep_start_time
            logger.info(f"TRACE - 结束准备LLM输入 - 耗时: {llm_prep_elapsed_time:.4f}秒")

            # --- Direct API Invocation ---
            logger.info("TRACE - 开始生成提示词")
            prompt_gen_start_time = time.time()
            # First generate the prompt
            prompt_value = prompt_template.invoke(prompt_input)
            prompt_gen_elapsed_time = time.time() - prompt_gen_start_time
            logger.info(f"TRACE - 结束生成提示词 - 耗时: {prompt_gen_elapsed_time:.4f}秒")

            # Setup output parser for JSON list/object based on is_repeat
            logger.info("TRACE - 开始设置输出解析器")
            parser_setup_start_time = time.time()
            if is_repeat == 1:
                output_parser = JsonOutputParser(pydantic_object=List[Dict[str, Any]])
                logger.info("ICRC Extractor: Expecting a JSON list from LLM (isRepeat=1)")
            else:
                output_parser = JsonOutputParser(pydantic_object=Dict[str, Any])
                logger.info("ICRC Extractor: Expecting a JSON object from LLM (isRepeat=0)")
            parser_setup_elapsed_time = time.time() - parser_setup_start_time
            logger.info(f"TRACE - 结束设置输出解析器 - 耗时: {parser_setup_elapsed_time:.4f}秒")

            try:
                # 将 PromptValue 对象转换为文本
                logger.info("TRACE - 开始转换提示词为文本")
                prompt_convert_start_time = time.time()
                prompt_text = prompt_value.to_string()
                logger.info(f"Rendered prompt text: {prompt_text[:100]}...")
                prompt_convert_elapsed_time = time.time() - prompt_convert_start_time
                logger.info(f"TRACE - 结束转换提示词为文本 - 耗时: {prompt_convert_elapsed_time:.4f}秒")

                # 调用LLM进行提取
                extracted_results, success = call_llm_for_extraction(prompt_text, is_repeat, state)

                if not success:
                    logger.warning(f"ICRC Extractor: LLM extraction failed for visit {visit_id}.")
                    extracted_results = []

                # --- 处理提取结果并映射记录ID ---
                processed_results = process_extraction_results(extracted_results, record_contents_map, visit_id)
                current_visit_results.extend(processed_results)

                # Check for completeness if non-repeat form (is_repeat=0)
                logger.info("TRACE - 开始检查表单完整性")
                check_complete_start_time = time.time()
                if is_repeat == 0 and extracted_results:  # Check if we got any result
                    # Assuming the first result is the one to check for completeness
                    # This assumes LLM returns only one dict for non-repeat forms
                    first_result = extracted_results[0]
                    all_fields_present = all(field in first_result for field in target_field_names)
                    if all_fields_present:
                        found_complete_form = True
                        logger.info(f"ICRC Extractor: Found complete data for non-repeat form in visit {visit_id}.")
                        # The loop will break on the next data_item iteration due to the check at the start
                check_complete_elapsed_time = time.time() - check_complete_start_time
                logger.info(f"TRACE - 结束检查表单完整性 - 耗时: {check_complete_elapsed_time:.4f}秒")

                visit_processed = True  # Mark visit as processed if LLM call succeeded
                successful_visits += 1

            except Exception as e:
                logger.error(f"ICRC Extractor: LLM chain invocation failed for visit {visit_id}, data_item. Error: {e}")
                # Optionally add partial/error state to results
                current_visit_results.append({"error": str(e), "visit_id": visit_id})

            data_item_elapsed_time = time.time() - data_item_start_time
            logger.info(f"TRACE - 结束处理data_item - 耗时: {data_item_elapsed_time:.4f}秒")

        # --- Aggregate results for the entire visit ---
        logger.info("TRACE - 开始聚合visit结果")
        aggregate_start_time = time.time()
        if current_visit_results:
            visit_results.extend(current_visit_results)  # Add results from this visit
        elif not visit_processed:
            # Handle visits with no processable data_items or where all failed
            logger.warning(f"ICRC Extractor: No data successfully processed or extracted for visit {visit_id}.")
            # Decide if an empty result or specific marker should be added
            # visit_results.append({"visit_id": visit_id, "status": "no_data_extracted"}) # Example
        aggregate_elapsed_time = time.time() - aggregate_start_time
        logger.info(f"TRACE - 结束聚合visit结果 - 耗时: {aggregate_elapsed_time:.4f}秒")

        single_visit_elapsed_time = time.time() - single_visit_start_time
        logger.info(f"TRACE - 结束处理visit: {visit_id} - 耗时: {single_visit_elapsed_time:.4f}秒")

    visit_process_elapsed_time = time.time() - visit_process_start_time
    logger.info(f"TRACE - 结束处理visit数据 - 总耗时: {visit_process_elapsed_time:.4f}秒")

    # --- 5. Update State with Extracted Data ---
    logger.info(f"--- Finalizing ICRC Form Data Extraction ({len(visit_results)} potential results) ---")
    logger.info("TRACE - 开始更新状态和生成最终结果")
    final_process_start_time = time.time()

    final_table_list: List[CrfTable] = []
    total_filled_fields_count = 0

    # Helper function to find data_item details by record_id
    def find_data_item_for_record(target_record_id: str) -> Optional[Tuple[Any, Any]]:
        for v in state.app_params.metaInfo.visits:
            for di in v.data:
                for r in di.records:
                    if r.recordId == target_record_id:
                        return v, di  # Return the visit and data_item containing the record
        return None, None

    if is_repeat == 1:
        # Repeat Form: Each result_item potentially creates a new table row
        logger.info("TRACE - 开始处理循环表单结果")
        repeat_start_time = time.time()

        logger.info("Processing results for REPEAT form (isRepeat=1)")
        for item_index, result_item in enumerate(visit_results):
            if "error" in result_item:
                logger.warning(f"Skipping error item: {result_item}")
                continue

            # Ensure essential keys are present
            if '__found_record_id__' not in result_item or '__visit_id__' not in result_item:
                logger.warning(
                    f"Skipping result item due to missing '__found_record_id__' or '__visit_id__': {result_item}")
                continue

            item_table = copy.deepcopy(target_table)  # Create a new table instance for this item
            item_table.visitId = str(result_item['__visit_id__'])
            item_filled_count = 0
            found_record_id = result_item['__found_record_id__']

            # Find the associated visit and data_item for DataSource details
            original_visit, original_data_item = find_data_item_for_record(found_record_id)

            for index_item in item_table.crfIndexList:
                field_name = index_item.crfIndexName
                if field_name in result_item and result_item[field_name] is not None:
                    value = result_item[field_name]
                    # Find the corresponding hit_value for this specific field
                    hit_value_key = f"{field_name}_hit_value"
                    field_hit_value = result_item.get(hit_value_key)
                    # Add warning log if hit_value is missing
                    if field_hit_value is None:
                        logger.warning(
                            f"ICRC DataSource: Missing '{hit_value_key}' for field '{field_name}' "
                            f"in result_item associated with record_id '{found_record_id}' (Visit: {item_table.visitId})."
                        )

                    index_item.crfIndexValue = str(value)  # Ensure value is string
                    item_filled_count += 1
                    total_filled_fields_count += 1

                    # Create DataSource with the correct field-specific hitValue
                    if original_data_item:
                        original_text = recordContentMap[found_record_id]
                        position = find_start_position(original_text, field_hit_value, index_item.crfIndexValue)
                        startPointIndex = 0
                        endPointIndex = 0
                        if position is not None:
                            startPointIndex = position.start
                            endPointIndex = position.end
                        index_item.dataSource = [
                            DataSource(
                                recordId=found_record_id,
                                docId=original_data_item.docId if hasattr(original_data_item, 'docId') else None,
                                tableName=original_data_item.tableName if hasattr(original_data_item,
                                                                                  'tableName') else None,
                                tableNameDesc=original_data_item.tableNameDesc if hasattr(original_data_item,
                                                                                          'tableNameDesc') else None,
                                hitValue=field_hit_value,
                                startPointIndex=startPointIndex,
                                endPointIndex=endPointIndex
                            )
                        ]
                    else:
                        # Fallback even if original data item not found, still try to include hitValue
                        index_item.dataSource = [DataSource(recordId=found_record_id, hitValue=field_hit_value)]
                        logger.warning(
                            f"Could not find original data_item for record_id {found_record_id} when creating DataSource for field {field_name}")
            # Only add the table if at least one field was filled for this item
            if item_filled_count > 0:
                final_table_list.append(item_table)
                logger.info(
                    f"Added table for repeat item {item_index} (Visit: {item_table.visitId}, Record: {found_record_id}) with {item_filled_count} fields.")
            else:
                logger.info(
                    f"Skipping empty table for repeat item {item_index} (Visit: {result_item.get('__visit_id__')}, Record: {found_record_id}).")

        repeat_elapsed_time = time.time() - repeat_start_time
        logger.info(f"TRACE - 结束处理循环表单结果 - 耗时: {repeat_elapsed_time:.4f}秒")

    else:  # is_repeat == 0
        # Non-Repeat Form: Accumulate data until one complete form is found
        logger.info("TRACE - 开始处理非循环表单结果")
        non_repeat_start_time = time.time()

        logger.info("Processing results for NON-REPEAT form (isRepeat=0)")
        accumulated_data = {}
        field_data_sources = {}
        found_all_fields = False
        final_table_visit_id = None

        for result_item in visit_results:
            if "error" in result_item or '__found_record_id__' not in result_item or '__visit_id__' not in result_item:
                continue  # Skip error or incomplete items

            if final_table_visit_id is None:  # Capture the visit ID from the first valid item contributing
                final_table_visit_id = str(result_item['__visit_id__'])

            found_record_id = result_item['__found_record_id__']
            original_visit, original_data_item = find_data_item_for_record(found_record_id)

            for field_name in target_field_names:
                if field_name not in accumulated_data and field_name in result_item and result_item[
                    field_name] is not None:
                    accumulated_data[field_name] = str(result_item[field_name])  # Ensure string
                    total_filled_fields_count += 1

                    # Find the corresponding hit_value for this specific field
                    hit_value_key = f"{field_name}_hit_value"
                    field_hit_value = result_item.get(hit_value_key)
                    # Add warning log if hit_value is missing
                    if field_hit_value is None:
                        logger.warning(
                            f"ICRC DataSource: Missing '{hit_value_key}' for field '{field_name}' "
                            f"in result_item associated with record_id '{found_record_id}' (Visit: {final_table_visit_id})."
                        )

                    # Create DataSource with the correct hitValue
                    datasource_list = []
                    if original_data_item:
                        original_text = recordContentMap[found_record_id]
                        position = find_start_position(original_text, field_hit_value, accumulated_data[field_name])
                        startPointIndex = 0
                        endPointIndex = 0
                        if position is not None:
                            startPointIndex = position.start
                            endPointIndex = position.end
                        datasource_list = [
                            DataSource(
                                recordId=found_record_id,
                                docId=original_data_item.docId if hasattr(original_data_item, 'docId') else None,
                                tableName=original_data_item.tableName if hasattr(original_data_item,
                                                                                  'tableName') else None,
                                tableNameDesc=original_data_item.tableNameDesc if hasattr(original_data_item,
                                                                                          'tableNameDesc') else None,
                                hitValue=field_hit_value,
                                startPointIndex=startPointIndex,
                                endPointIndex=endPointIndex
                            )
                        ]
                    else:
                        # Fallback even if original data item not found
                        datasource_list = [DataSource(recordId=found_record_id, hitValue=field_hit_value)]
                        logger.warning(
                            f"Could not find original data_item for record_id {found_record_id} when creating DataSource for field {field_name}")
                    field_data_sources[field_name] = datasource_list  # Store the created DataSource list

            # Check if all target fields are now accumulated
            if len(accumulated_data) == len(target_field_names):
                found_all_fields = True
                logger.info(f"Found all required fields for non-repeat form (Visit: {final_table_visit_id}).")
                break  # Stop processing more results once complete form is found

        # Create the single table if data was accumulated
        logger.info("TRACE - 开始创建非循环表单")
        create_table_start_time = time.time()

        if accumulated_data:
            final_table = copy.deepcopy(target_table)
            final_table.visitId = final_table_visit_id if final_table_visit_id else "unknown"
            item_filled_count = 0

            for index_item in final_table.crfIndexList:
                field_name = index_item.crfIndexName
                if field_name in accumulated_data:
                    index_item.crfIndexValue = accumulated_data[field_name]
                    index_item.dataSource = field_data_sources.get(field_name, [])
                    item_filled_count += 1

            final_table_list.append(final_table)
            logger.info(
                f"Added single table for non-repeat form (Visit: {final_table.visitId}) with {item_filled_count} fields.")
        elif not visit_results:  # Handle case where visit_results was empty
            logger.warning("No valid results found to populate non-repeat form.")
        else:  # Handle case where loop finished but form not complete
            logger.warning("Could not accumulate all required fields for non-repeat form.")
            # Optionally create a partially filled table if desired
            # final_table = copy.deepcopy(target_table)
            # ... populate partial data ...
            # final_table_list.append(final_table)

        create_table_elapsed_time = time.time() - create_table_start_time
        logger.info(f"TRACE - 结束创建非循环表单 - 耗时: {create_table_elapsed_time:.4f}秒")

        non_repeat_elapsed_time = time.time() - non_repeat_start_time
        logger.info(f"TRACE - 结束处理非循环表单结果 - 耗时: {non_repeat_elapsed_time:.4f}秒")

    # --- Generate Final Response Message ---
    logger.info("TRACE - 开始生成最终响应消息")
    response_start_time = time.time()

    # 无论final_table_list是否为空，都按正常流程返回结果
    # final_table_list为0时，可能是匹配的字段本来就都没有找出来，这是正常情况
    if not final_table_list:
        # 当final_table_list为空时，为每个visit创建一个表单结构，将crfIndexValue设置为空字符串
        logger.info("TRACE - 开始为每个visit设置空字段值")
        empty_table_start_time = time.time()

        # 为每个visit创建一个表单结构
        if visits and len(visits) > 0:
            for visit in visits:
                # 使用入参的表单结构，将所有字段的crfIndexValue设置为空字符串
                visit_table = copy.deepcopy(target_table)
                for index_item in visit_table.crfIndexList:
                    index_item.crfIndexValue = ""  # 设置为空字符串

                # 设置当前visit的visitId
                visit_table.visitId = visit.visitId

                # 将表单添加到final_table_list中
                final_table_list.append(visit_table)
                logger.info(f"Created empty table for visitId: {visit.visitId}")
        else:
            # 如果没有visits，创建一个默认的空表单
            empty_table = copy.deepcopy(target_table)
            for index_item in empty_table.crfIndexList:
                index_item.crfIndexValue = ""  # 设置为空字符串
            empty_table.visitId = "unknown"
            final_table_list.append(empty_table)
            logger.info("Created empty table with visitId: unknown")

        empty_table_elapsed_time = time.time() - empty_table_start_time
        logger.info(f"TRACE - 结束为每个visit设置空字段值 - 耗时: {empty_table_elapsed_time:.4f}秒")

        # 根据处理情况生成相应的消息
        if successful_visits > 0:
            final_response_message = "AI已成功分析记录，但在当前数据中未找到与表单定义匹配的字段值。"
        elif visit_count > 0:
            final_response_message = "AI已完成记录分析，但未能从数据中提取到表单字段值。"
        else:
            final_response_message = "AI已完成分析，但没有找到可处理的访问记录。"
        logger.info(f"Created {len(final_table_list)} empty tables for {len(visits) if visits else 0} visits")
    else:
        num_tables = len(final_table_list)
        table_text = "表单" if num_tables == 1 else "表单"
        field_text = "字段" if total_filled_fields_count == 1 else "字段"
        processed_visits = len(set(t.visitId for t in final_table_list if t.visitId))
        visit_text = "电子源" if processed_visits == 1 else "电子源"

        if is_repeat == 1:
            final_response_message = f"成功提取数据，已从{processed_visits}个{visit_text}中创建{num_tables}个{table_text}，共填充{total_filled_fields_count}个{field_text}。"
        else:  # is_repeat == 0
            if found_all_fields:
                final_response_message = f"成功提取数据，已从{visit_text}{final_table_list[0].visitId}中创建1个完整{table_text}，共填充{total_filled_fields_count}个{field_text}。"
            else:
                final_response_message = f"已提取部分数据，从{visit_text}{final_table_list[0].visitId}中创建1个{table_text}，共填充{total_filled_fields_count}个{field_text}。未能找到所有必填字段。"

    logger.info(f"Final response: {final_response_message}")

    response_elapsed_time = time.time() - response_start_time
    logger.info(f"TRACE - 结束生成最终响应消息 - 耗时: {response_elapsed_time:.4f}秒")

    # 计算总耗时
    final_process_elapsed_time = time.time() - final_process_start_time
    logger.info(f"TRACE - 结束更新状态和生成最终结果 - 总耗时: {final_process_elapsed_time:.4f}秒")

    # Return the final state update
    # Ensure the key matches what the graph expects (e.g., 'icrc_form_data' or 'crf_result')
    # Based on original return, seems 'crf_result' might be expected by graph edge/conditional logic
    return {
        "thought_log": state.thought_log,
        "icrc_intent": state.icrc_intent,
        "crf_result": final_table_list,
        "final_response": final_response_message,
        "visit_results": visit_results,
        "model_name": os.getenv("LLM_MODEL_NAME"),
        "knowledge_slice": state.knowledge_slice
    }


@trace_time("转换知识切片")
def convert_to_knowledge_slice(doc_result):
    """
    将DocumentResult转换为KnowledgeSlice格式

    Args:
        doc_result: DocumentResult对象

    Returns:
        KnowledgeSlice对象
    """
    # 从metadata中获取source作为fileName
    file_name = doc_result.metadata.get("source", "未知文件")

    # 从metadata中获取doc_id作为fileId
    file_id = doc_result.metadata.get("doc_id", "")

    # 不创建Position对象，而是直接使用字典格式
    position = {
        "context": doc_result.content,
        "pageNo": doc_result.metadata.get("pageNo"),
        "start": doc_result.metadata.get("start", 0),
        "end": doc_result.metadata.get("end", 0)
    }

    # 创建并返回KnowledgeSlice对象
    return KnowledgeSlice(
        fileId=file_id,
        fileName=file_name,
        fileUrl=doc_result.metadata.get("fileUrl"),
        tag=1,  # 默认值
        start=doc_result.metadata.get("start", 0),
        end=doc_result.metadata.get("end", 0),
        context=doc_result.content,
        position=position
    )


@trace_time("检索知识库")
def recallKnowledge(knowledge, target_field_names, state=None):
    field_knowledge_map = {}
    knowledge_context = ""
    knowledge_slices = []  # 用于存储转换后的KnowledgeSlice对象

    # 处理用户上传的知识
    user_knowledge_context = ""
    if state and state.user_uploaded_knowledge and state.user_uploaded_knowledge_content:
        logger.info("TRACE - 开始处理用户上传的知识")
        user_start_time = time.time()

        # 打印详细信息以便调试
        logger.info(f"recallKnowledge: 用户上传知识数量: {len(state.user_uploaded_knowledge)}")
        logger.info(f"recallKnowledge: 用户上传知识内容映射键数量: {len(state.user_uploaded_knowledge_content)}")

        user_knowledge = state.user_uploaded_knowledge
        user_knowledge_content = state.user_uploaded_knowledge_content

        if user_knowledge and user_knowledge_content:
            user_knowledge_sections = []
            for k_file in user_knowledge:
                file_id = k_file.fileId
                logger.info(f"recallKnowledge: 处理知识文件 fileId={file_id}, fileName={k_file.fileName}")

                if file_id in user_knowledge_content:
                    content = user_knowledge_content[file_id]
                    logger.info(f"recallKnowledge: 找到知识内容，长度={len(content)}")

                    # 创建KnowledgeSlice对象
                    position = {
                        "context": content,
                        "pageNo": None,
                        "start": 0,
                        "end": len(content)
                    }

                    knowledge_slice = KnowledgeSlice(
                        fileId=file_id,
                        fileName=k_file.fileName,
                        fileUrl=k_file.fileUrl,
                        tag=1,
                        start=0,
                        end=len(content),
                        context=content,
                        position=position
                    )
                    knowledge_slices.append(knowledge_slice)
                    logger.info(f"recallKnowledge: 创建了KnowledgeSlice对象，添加到knowledge_slices")

                    # 为每个目标字段添加用户上传的知识
                    for field_name in target_field_names:
                        if field_name not in field_knowledge_map:
                            field_knowledge_map[field_name] = []
                        # 添加用户上传的知识到字段知识映射
                        field_knowledge_map[field_name].append(f"[用户上传] {content}")
                        logger.info(f"recallKnowledge: 为字段'{field_name}'添加了用户上传的知识")

                    # 添加到用户知识上下文
                    escaped_content = content.replace('{', '{{').replace('}', '}}')
                    user_knowledge_sections.append(f"## 用户上传知识\n{escaped_content}")
                    logger.info(f"recallKnowledge: 添加到用户知识上下文")
                else:
                    logger.warning(f"recallKnowledge: 未找到知识内容: fileId={file_id}")

            if user_knowledge_sections:
                user_knowledge_context = "\n\n".join(user_knowledge_sections)
                logger.info(f"ICRC Extractor: 处理了{len(user_knowledge)}条用户上传的知识")
                logger.info(f"recallKnowledge: 用户知识上下文长度={len(user_knowledge_context)}")
            else:
                logger.warning("recallKnowledge: 没有生成用户知识上下文")
        else:
            logger.warning("recallKnowledge: user_knowledge或user_knowledge_content为空")

        user_elapsed_time = time.time() - user_start_time
        logger.info(f"TRACE - 结束处理用户上传的知识 - 耗时: {user_elapsed_time:.4f}秒")

    # 处理元信息中的知识库
    if knowledge and len(knowledge) > 0:
        try:
            # 初始化QueryService
            logger.info("TRACE - 开始初始化QueryService")
            init_start_time = time.time()
            query_service = QueryService()
            init_elapsed_time = time.time() - init_start_time
            logger.info(f"TRACE - 结束初始化QueryService - 耗时: {init_elapsed_time:.4f}秒")

            # 收集所有fileId作为doc_ids
            logger.info("TRACE - 开始收集fileId")
            collect_start_time = time.time()
            doc_ids = [k.fileId for k in knowledge if k.fileId]
            if not doc_ids:
                logger.warning("ICRC Extractor: No valid fileId found in knowledge")
            else:
                # 获取第一个knowledge的collectionName作为collection_name
                collection_name = knowledge[0].collectionName if knowledge[0].collectionName else "default"
            collect_elapsed_time = time.time() - collect_start_time
            logger.info(f"TRACE - 结束收集fileId - 耗时: {collect_elapsed_time:.4f}秒")

            if doc_ids:
                # 为每个字段单独检索
                logger.info("TRACE - 开始为每个字段检索知识库")
                for field_name in target_field_names:
                    field_start_time = time.time()
                    # 构建当前字段的查询问题
                    question = f"关于{field_name}的信息"
                    logger.info(f"ICRC Extractor: Querying knowledge base for field '{field_name}'")

                    # 构建查询请求
                    request = QueryRequest(
                        question=question,
                        collection_name=collection_name,
                        doc_ids=doc_ids
                    )

                    # 执行查询
                    query_start_time = time.time()
                    response = query_service.query(request)
                    query_elapsed_time = time.time() - query_start_time
                    logger.info(f"TRACE - 字段'{field_name}'查询耗时: {query_elapsed_time:.4f}秒")

                    # 处理查询结果
                    process_start_time = time.time()
                    if response and response.results and len(response.results) > 0:
                        # 初始化字段知识映射
                        if field_name not in field_knowledge_map:
                            field_knowledge_map[field_name] = []

                        # 将当前字段的检索结果添加到列表中
                        field_contents = [result.content for result in response.results]
                        field_knowledge_map[field_name].extend(field_contents)

                        logger.info(
                            f"ICRC Extractor: Retrieved {len(response.results)} knowledge items for field '{field_name}'")

                        # 将每个DocumentResult转换为KnowledgeSlice并添加到列表中
                        for result in response.results:
                            knowledge_slice = convert_to_knowledge_slice(result)
                            knowledge_slices.append(knowledge_slice)
                    else:
                        logger.info(f"ICRC Extractor: No knowledge retrieved for field '{field_name}'")
                    process_elapsed_time = time.time() - process_start_time
                    logger.info(f"TRACE - 字段'{field_name}'结果处理耗时: {process_elapsed_time:.4f}秒")

                    field_elapsed_time = time.time() - field_start_time
                    logger.info(f"TRACE - 字段'{field_name}'总耗时: {field_elapsed_time:.4f}秒")

                # 将所有字段的知识整合到一起
                logger.info("TRACE - 开始整合知识")
                combine_start_time = time.time()
                if field_knowledge_map:
                    knowledge_sections = []
                    for field, contents in field_knowledge_map.items():
                        if contents:
                            # 将列表合并为字符串
                            field_content = "\n".join(contents)
                            if field_content.strip():
                                # Escape curly braces in content to prevent misinterpretation by LangChain
                                escaped_content = field_content.replace('{', '{{').replace('}', '}}')
                                knowledge_sections.append(f"## {field}\n{escaped_content}")

                    # 如果有用户上传的知识，添加到知识上下文的开头
                    if user_knowledge_context:
                        knowledge_sections.insert(0, user_knowledge_context)

                    knowledge_context = "\n\n".join(knowledge_sections)
                    logger.info(f"ICRC Extractor: Created knowledge context with {len(field_knowledge_map)} fields")
                    logger.info(f"ICRC Extractor: Created {len(knowledge_slices)} knowledge slices")
                elif user_knowledge_context:
                    # 如果没有从知识库检索到内容，但有用户上传的知识，则使用用户上传的知识
                    knowledge_context = user_knowledge_context
                    logger.info("ICRC Extractor: Using only user uploaded knowledge")
                else:
                    logger.info("ICRC Extractor: No knowledge retrieved for any field")
                combine_elapsed_time = time.time() - combine_start_time
                logger.info(f"TRACE - 结束整合知识 - 耗时: {combine_elapsed_time:.4f}秒")
        except Exception as e:
            logger.error(f"ICRC Extractor: Error retrieving knowledge: {e}", exc_info=True)
            # 出错时继续执行，不影响主流程
    elif user_knowledge_context:
        # 如果没有元信息中的知识，但有用户上传的知识，则使用用户上传的知识
        knowledge_context = user_knowledge_context
        logger.info("ICRC Extractor: Using only user uploaded knowledge (no metadata knowledge)")

    # 返回知识上下文和知识切片列表
    return knowledge_context, knowledge_slices


@trace_time("处理Refill_Form")
def handle_refill_form(state: AssistantState) -> Dict[str, Any]:
    """
    处理Refill_Form意图，根据form_name调用不同的模块

    Args:
        state: 当前助手状态

    Returns:
        Dict[str, Any]: 处理结果
    """
    logger.info("--- 执行 Refill_Form 处理 ---")

    # 获取意图详情
    if not state.icrc_extracted_details:
        logger.warning("Refill_Form处理: 缺少意图详情")
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "final_response": "无法处理请求，缺少必要的表单信息。",
            "model_name": os.getenv("LLM_MODEL_NAME")
        }

    # 获取form_name和table_code
    form_name = state.icrc_source
    # 直接从state中获取table_code，与获取form_name的方式保持一致
    table_code = state.table_code if hasattr(state, 'table_code') else None
    logger.info(f"Refill_Form处理: 表单类型 = {form_name}, table_code = {table_code}")

    # 获取时间实体
    time_entities = state.icrc_extracted_details.time_entities if state.icrc_extracted_details else []

    # 准备参数
    # 从icrc_extracted_details中获取必要参数
    extracted_pts_id = state.icrc_extracted_details.subject_id if state.icrc_extracted_details else None

    # 如果state中没有table_code，则尝试从icrc_extracted_details中获取
    if table_code is None and state.icrc_extracted_details and hasattr(state.icrc_extracted_details, 'table_code'):
        table_code = state.icrc_extracted_details.table_code
        logger.info(f"从icrc_extracted_details获取table_code: {table_code}")

    # 然后从metaInfo中获取必要的参数
    # 首先检查是否有context.patientInfo
    if (hasattr(state.app_params.metaInfo, 'context') and
            state.app_params.metaInfo.context and
            hasattr(state.app_params.metaInfo.context, 'patientInfo') and
            state.app_params.metaInfo.context.patientInfo):
        # 从context.patientInfo获取
        patient_info = state.app_params.metaInfo.context.patientInfo
        pts_id = extracted_pts_id or patient_info.ptsId
        pro_id = patient_info.projectId
        empi_id = patient_info.empiId
        hospital_no = patient_info.hospitalNo
    else:
        # 从metaInfo直接获取
        pts_id = extracted_pts_id or state.app_params.metaInfo.ptsId
        pro_id = state.app_params.metaInfo.projectId
        empi_id = getattr(state.app_params.metaInfo, 'empiId', None)
        hospital_no = getattr(state.app_params.metaInfo, 'hospitalNo', None)

    logger.info(f"获取参数: pts_id={pts_id}, empi_id={empi_id}, pro_id={pro_id}, hospital_no={hospital_no}")

    # 默认时间参数
    start_time = None
    end_time = None
    biz_start_time = None
    biz_end_time = None
    time_type = None

    # 处理时间实体
    for entity in time_entities:
        # 确定时间类型
        biz_time_type = entity.biz_time_type if hasattr(entity, 'biz_time_type') else "业务时间"

        # 获取time_type
        entity_time_type = entity.time_type if hasattr(entity, 'time_type') and entity.time_type is not None else None
        if entity_time_type is not None:
            # 如果实体中已经有time_type，直接使用
            time_type = entity_time_type
            logger.info(f"从实体中获取time_type: {time_type}")
        elif entity.type == "relative_time" and entity.value == "最近一次":
            # 如果是"最近一次"，设置为1
            time_type = 1
            logger.info("检测到'最近一次'，设置time_type=1")
        elif entity.type == "date_range" and isinstance(entity.value, dict):
            # 如果是日期范围，设置为2
            time_type = 2
            logger.info("检测到日期范围，设置time_type=2")

        # 根据时间类型设置不同的参数
        if biz_time_type == "就诊时间":
            # 就诊时间使用start_time和end_time
            if entity.type == "date_point":
                start_time = entity.value
                end_time = entity.value
            elif entity.type == "date_range" and isinstance(entity.value, dict):
                start_time = entity.value.get("start_time")
                end_time = entity.value.get("end_time")
        elif biz_time_type == "业务时间":
            # 业务时间使用biz_start_time和biz_end_time
            if entity.type == "date_point":
                biz_start_time = entity.value
                biz_end_time = entity.value
            elif entity.type == "date_range" and isinstance(entity.value, dict):
                biz_start_time = entity.value.get("start_time")
                biz_end_time = entity.value.get("end_time")
        elif biz_time_type == "首次访视时间":
            # 首次访视时间需要调用baseline_visit.py
            try:
                # 导入baseline_visit模块
                baseline_visit_module = importlib.import_module("app.function_call.baseline_visit")

                # 调用API函数获取首次访视时间
                if pts_id:
                    baseline_result = baseline_visit_module.get_baseline_visit_date_api({"pts_id": pts_id})

                    if baseline_result.get("success") and baseline_result.get("data"):
                        baseline_date = baseline_result["data"].get("baseline_visit_date")
                        if baseline_date:
                            biz_start_time = baseline_date
                            biz_end_time = baseline_date
                            logger.info(f"获取到首次访视时间: {baseline_date}")
                        else:
                            logger.warning(f"未找到患者 {pts_id} 的首次访视时间")
                    else:
                        logger.warning(f"获取首次访视时间失败: {baseline_result.get('message')}")
                else:
                    logger.warning("缺少pts_id参数，无法获取首次访视时间")
            except Exception as e:
                logger.error(f"调用baseline_visit模块出错: {str(e)}")

    # 根据form_name调用不同的模块
    result = None

    try:
        if form_name == "Structure":
            # 调用structure.py
            logger.info("调用structure.py处理结构化数据")

            # 准备参数
            # 直接使用从state中获取的table_code，如果没有则使用默认值
            params = {
                "table_code": table_code,  # 优先使用从state中获取的table_code
                "empi_id": empi_id,
                "pts_id": pts_id,
                "pro_id": pro_id,
                "hospital_no": hospital_no,
                "time_type": time_type,
                "start_time": start_time,
                "end_time": end_time,
                "biz_start_time": biz_start_time,
                "biz_end_time": biz_end_time
            }
            logger.info(f"调用structure.py使用的table_code: {params['table_code']}")

            # 过滤掉None值
            filtered_params = {k: v for k, v in params.items() if v is not None}

            # 调用workflow_function
            result = structure.workflow_function(**filtered_params)

        elif form_name == "Paper":
            # 调用paper.py
            logger.info("调用paper.py处理纸质文件数据")


            # 准备参数
            # 将state.app_params.questions中role为"user"的content字符串全部拼接在一起
            natural_language = ""
            for msg in state.app_params.questions:
                if msg.role == "user":
                    natural_language += msg.content + " "
            natural_language = natural_language.strip()  # 去除末尾多余空格
            logger.info(f"拼接后的natural_language: {natural_language[:100]}...")  # 记录日志，只显示前100个字符

            # 获取design_version_id
            design_version_id = state.app_params.metaInfo.context.patientInfo.designVersionId

            # 直接使用从state中获取的table_code，如果没有则使用默认值
            default_table_code = state.app_params.metaInfo.crfInfo.tableList[
                0].crfTableName if state.app_params.metaInfo.crfInfo.tableList else None
            params = {
                "naturalLanguage": natural_language,
                "pts_id": pts_id,
                "pro_id": pro_id,
                "design_version_id": design_version_id,
                "table_code": table_code or default_table_code,  # 优先使用从state中获取的table_code
                "time_type": time_type,
                "start_time": start_time,
                "end_time": end_time,
                "biz_start_time": biz_start_time,
                "biz_end_time": biz_end_time
            }
            logger.info(f"调用paper.py使用的table_code: {params['table_code']}")

            # 过滤掉None值
            filtered_params = {k: v for k, v in params.items() if v is not None}

            # 调用transform_paper_entity_api
            result = paper.transform_paper_entity_api(filtered_params)

        elif form_name == "Medical_Record":
            # 预留接口，暂不做操作
            logger.info("Medical_Record类型暂不支持")
            return {
                "thought_log": getattr(state, 'thought_log', []),
                "icrc_intent": state.icrc_intent,
                "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
                "crf_result": [],
                "final_response": "病历类型的表单重填功能暂未实现，请稍后再试。",
                "model_name": os.getenv("LLM_MODEL_NAME"),
                "table_code": table_code  # 添加table_code字段
            }
        elif form_name == "未指定表单":
            # 处理"未指定表单"的情况，直接使用入参中的visits数据
            logger.info("检测到'未指定表单'，直接使用入参中的visits数据")

            # 直接从state.app_params.metaInfo中获取visits数据
            visits = state.app_params.metaInfo.visits

            if not visits:
                logger.warning("入参中没有visits数据")
                return {
                    "thought_log": getattr(state, 'thought_log', []),
                    "icrc_intent": state.icrc_intent,
                    "icrc_extracted_details": state.icrc_extracted_details,
                    "crf_result": [],
                    "final_response": "未找到访视数据，请检查后重试。",
                    "model_name": os.getenv("LLM_MODEL_NAME"),
                    "table_code": table_code
                }

            # 将visits数据转换为字典格式，以便返回
            visits_data = []
            for visit in visits:
                visit_dict = {
                    "visitId": visit.visitId,
                    "visitType": visit.visitType,
                    "data": []
                }

                for data_item in visit.data:
                    data_dict = {
                        "docId": data_item.docId,
                        "isStruct": data_item.isStruct,
                        "tableNameDesc": data_item.tableNameDesc,
                        "tableName": data_item.tableName,
                        "records": []
                    }

                    for record in data_item.records:
                        record_dict = {
                            "content": record.content,
                            "recordId": record.recordId
                        }
                        data_dict["records"].append(record_dict)

                    visit_dict["data"].append(data_dict)

                visits_data.append(visit_dict)

            logger.info(f"成功从入参中获取到 {len(visits_data)} 个visits数据")

            # 构建返回结果，包含visits数据
            return {
                "thought_log": getattr(state, 'thought_log', []),
                "icrc_intent": state.icrc_intent,
                "icrc_extracted_details": state.icrc_extracted_details,
                "crf_result": [],
                "final_response": "成功获取访视信息，将继续处理表单填写。",
                "model_name": os.getenv("LLM_MODEL_NAME"),
                "visits": visits_data,  # 添加visits数据
                "table_code": table_code
            }
        else:
            logger.warning(f"未知的表单类型: {form_name}")
            return {
                "thought_log": getattr(state, 'thought_log', []),
                "icrc_intent": state.icrc_intent,
                "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
                "crf_result": [],
                "final_response": f"不支持的表单类型: {form_name}",
                "model_name": os.getenv("LLM_MODEL_NAME"),
                "table_code": table_code  # 添加table_code字段
            }
    except Exception as e:
        logger.error(f"处理Refill_Form时发生错误: {str(e)}")
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "final_response": f"处理请求时发生错误: {str(e)}",
            "model_name": os.getenv("LLM_MODEL_NAME"),
            "table_code": table_code  # 添加table_code字段
        }

    # 处理结果
    if not result:
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "final_response": "未能获取有效数据，请检查参数后重试。",
            "model_name": os.getenv("LLM_MODEL_NAME"),
            "table_code": table_code  # 添加table_code字段
        }

    # 检查结果是否成功
    if result.get("success") == False:
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "visits": [],
            "final_response": f"获取数据失败: {result.get('message')}",
            "model_name": os.getenv("LLM_MODEL_NAME"),
            "table_code": table_code  # 添加table_code字段
        }

    # 提取结果中的数据
    transform_results = result.get("transform_results")

    if not transform_results:
        return {
            "thought_log": getattr(state, 'thought_log', []),
            "icrc_intent": state.icrc_intent,
            "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details
            "crf_result": [],
            "final_response": "转换数据失败，未获取到有效结果。",
            "model_name": os.getenv("LLM_MODEL_NAME"),
            "table_code": table_code  # 添加table_code字段
        }

    # 构建返回结果
    return {
        "thought_log": getattr(state, 'thought_log', []),
        "icrc_intent": state.icrc_intent,
        "icrc_extracted_details": state.icrc_extracted_details,  # 透传icrc_extracted_details，包含table_code
        "crf_result": transform_results.get("data", []),
        "final_response": f"成功获取并转换数据: {result.get('message')}",
        "model_name": os.getenv("LLM_MODEL_NAME"),
        "visits": result.get("visits").get("data"),  # 添加visits数据
        "table_code": table_code  # 直接添加table_code字段
    }
