"""
直接调用 LLM API 的工具函数，用于获取思维链
"""
import json
import logging
import os
import uuid
from contextvars import ContextVar
from queue import Queue, Full
from typing import Optional, Tuple, Generator, Any

from openai import OpenAI, APIError, APITimeoutError
from app.domain.schemas import StreamEvent, StreamEventType

logger = logging.getLogger(__name__)

stream_queue_var: ContextVar[Optional[Queue]] = ContextVar("stream_queue", default=None)

_openrouter_client = None

def get_openrouter_client() -> Optional[OpenAI]:
    global _openrouter_client
    if _openrouter_client is None:
        api_key = os.getenv("OPENROUTER_API_KEY")
        base_url = os.getenv("OPENROUTER_API_BASE")
        if not api_key or not base_url:
            missing_vars = [v for v, k in [("OPENROUTER_API_KEY", api_key), ("OPENROUTER_API_BASE", base_url)] if not k]
            logger.warning(f"Missing OpenRouter config ({', '.join(missing_vars)}), cannot init client")
            return None
        logger.info(f"Initializing OpenRouter client with base_url: {base_url}")
        try:
            _openrouter_client = OpenAI(
                api_key=api_key,
                base_url=base_url,
                default_headers={
                    "HTTP-Referer": os.getenv("HTTP_REFERER", "http://localhost"),
                    "X-Title": os.getenv("X_TITLE", "Prime RAG API")
                },
                timeout=300.0,
            )
        except Exception as e:
            logger.error(f"Failed to init OpenRouter client: {e}", exc_info=True)
            return None
    return _openrouter_client

def _handle_stream(
    response_stream: Generator[Any, None, None],
    queue: Queue,
    message_id: str,
    provider_name: str,
    is_intent_recognition: bool = False,
    is_form_extraction: bool = False
) -> Tuple[str, Optional[str]]:
    accumulated_content = ""
    accumulated_thinking = ""
    error_occurred = False
    try:
        logger.debug(f"[_handle_stream] Got queue from contextvar: {type(queue)}")
        is_streaming_context = True
        connect_event = StreamEvent(id=message_id, event=StreamEventType.CONNECTING, content=f"Connecting to {provider_name}...")
        try:
            queue.put_nowait(connect_event.model_dump_json())
        except Full:
            logger.error(f"[_handle_stream] Stream queue is full. Discarding event: {connect_event.event}")
        except Exception as q_err:
             logger.error(f"[_handle_stream] Error putting event to queue: {q_err}", exc_info=True)
        logger.debug(f"Stream {message_id}:CONNECTING sent")
        for chunk in response_stream:
            delta = chunk.choices[0].delta if chunk.choices and chunk.choices[0].delta else None
            if delta:
                thinking_content = None
                thinking_keys = ["reasoning", "reasoning_content", "thinking"]
                for key in thinking_keys:
                    if hasattr(delta, key) and getattr(delta, key):
                        thinking_content = getattr(delta, key)
                        accumulated_thinking += thinking_content
                        think_event = StreamEvent(id=message_id, event=StreamEventType.THINKING, content=thinking_content)
                        try:
                            logger.debug(f"[_handle_stream] Attempting to put event to queue: {think_event.event }")
                            queue.put_nowait(think_event.model_dump_json())
                            logger.debug(f"[_handle_stream] Successfully put event: {think_event.event }")
                        except Full:
                            logger.error(f"[_handle_stream] Stream queue is full. Discarding event: {think_event.event}")
                        except Exception as q_err:
                             logger.error(f"[_handle_stream] Error putting event to queue: {q_err}", exc_info=True)
                        logger.debug(f"Stream {message_id}:THINKING sent: {thinking_content[:50]}...")
                        break
                if hasattr(delta, 'content') and delta.content:
                    answer_content = delta.content
                    accumulated_content += answer_content

                    # 如果是意图识别阶段或表单信息提取阶段，不发送answering事件
                    if not is_intent_recognition and not is_form_extraction:
                        answer_event = StreamEvent(id=message_id, event=StreamEventType.ANSWERING, content=answer_content)
                        try:
                            queue.put_nowait(answer_event.model_dump_json())
                        except Full:
                            logger.error(f"[_handle_stream] Stream queue is full. Discarding event: {answer_event.event}")
                        except Exception as q_err:
                             logger.error(f"[_handle_stream] Error putting event to queue: {q_err}", exc_info=True)
                        logger.debug(f"Stream {message_id}:ANSWERING sent: {answer_content[:50]}...")
    except (APIError, APITimeoutError) as e:
        logger.error(f"Stream {message_id}: API Error from {provider_name}: {e}", exc_info=True)
        error_event = StreamEvent(id=message_id, event=StreamEventType.ERROR, content=f"API Error: {str(e)}")
        try:
            logger.debug(f"[_handle_stream] Attempting to put ERROR event to queue.")
            queue.put_nowait(error_event.model_dump_json())
            logger.debug(f"[_handle_stream] Successfully put ERROR event.")
        except Full:
            logger.error(f"[_handle_stream] Stream queue full. Discarding ERROR event.")
        except Exception as q_err:
             logger.error(f"[_handle_stream] Error putting ERROR event to queue: {q_err}", exc_info=True)
        error_occurred = True
        accumulated_content = f"API 调用错误: {str(e)}"
        accumulated_thinking = None
    except Exception as e:
        logger.error(f"Stream {message_id}: Unexpected Error from {provider_name}: {e}", exc_info=True)
        error_event = StreamEvent(id=message_id, event=StreamEventType.ERROR, content=f"Unexpected Error: {str(e)}")
        try:
            logger.debug(f"[_handle_stream] Attempting to put ERROR event to queue.")
            queue.put_nowait(error_event.model_dump_json())
            logger.debug(f"[_handle_stream] Successfully put ERROR event.")
        except Full:
            logger.error(f"[_handle_stream] Stream queue full. Discarding ERROR event.")
        except Exception as q_err:
             logger.error(f"[_handle_stream] Error putting ERROR event to queue: {q_err}", exc_info=True)
        error_occurred = True
        accumulated_content = f"处理流式响应时发生意外错误: {str(e)}"
        accumulated_thinking = None
    finally:
        logger.debug(f"[_handle_stream] Finished handling stream for message {message_id}. Error occurred: {error_occurred}")

    return accumulated_content, accumulated_thinking if accumulated_thinking else None

def call_llm_api(
        prompt: str,
        provider: str = "openrouter",
        model: str = None,
        temperature: float = 0.0,
        max_tokens: int = 128000,
        is_intent_recognition: bool = False,
        is_form_extraction: bool = False
) -> Tuple[str, Optional[str]]:
    queue = stream_queue_var.get()
    is_streaming = queue is not None
    client: Optional[OpenAI] = None
    provider_name: str = ""
    model_to_use: str = ""

    if provider.lower() == "openrouter":
        provider_name = "OpenRouter"
        client = get_openrouter_client()
        if not client:
            logger.error("Cannot get OpenRouter client, check config/logs")
            return "API client init failed", None
        model_to_use = model or os.getenv("OPENROUTER_DEFAULT_MODEL", "deepseek/deepseek-chat")
        extra_args = {}
    else:
        logger.error(f"Unsupported provider: {provider}")
        return f"Unsupported provider: {provider}", None

    try:
        logger.info(f"Request to {provider_name} (model: {model_to_use}, stream: {is_streaming}, is_intent_recognition: {is_intent_recognition}, is_form_extraction: {is_form_extraction})")
        logger.debug(f"Prompt: {prompt[:200]}...")

        if is_streaming:
            message_id = f"msg_{uuid.uuid4().hex[:6]}"
            logger.info(f"Starting stream, Message ID: {message_id}")
            response_stream = client.chat.completions.create(
                model=model_to_use,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True,
                **extra_args
            )
            content, thinking_process = _handle_stream(response_stream, queue, message_id, provider_name, is_intent_recognition, is_form_extraction)
            logger.info(f"Stream finished, Message ID: {message_id}")
            return content, thinking_process
        else:
            logger.info("Executing non-streaming call")
            response = client.chat.completions.create(
                model=model_to_use,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                max_tokens=max_tokens,
                stream=False,
                **extra_args
            )
            try:
                response_log = response.model_dump(exclude_unset=True)
                logger.debug(f"{provider_name} non-stream response: {json.dumps(response_log, ensure_ascii=False, indent=2)}")
            except Exception as log_err:
                logger.warning(f"Could not serialize non-stream response for logging: {log_err}")
            content = response.choices[0].message.content
            thinking_process = None
            message = response.choices[0].message
            thinking_keys = ["reasoning_content", "reasoning", "thinking"]
            for key in thinking_keys:
                if hasattr(message, key) and getattr(message, key):
                    thinking_process = getattr(message, key)
                    logger.info(f"Extracted thinking process from message (key: {key})")
                    break
            if not thinking_process and hasattr(message, 'model_extra') and message.model_extra:
                 for key in thinking_keys:
                     if key in message.model_extra:
                         thinking_process = message.model_extra[key]
                         logger.info(f"Extracted thinking process from message.model_extra (key: {key})")
                         break
            if thinking_process:
                 logger.debug(f"Thinking process: {thinking_process[:100]}...")
            else:
                 logger.info("No explicit thinking process found in response")
            return content, thinking_process

    except (APIError, APITimeoutError) as e:
        logger.error(f"API Error calling {provider_name} (model: {model_to_use}): {e}", exc_info=True)
        if is_streaming and queue:
            message_id = f"msg_{uuid.uuid4().hex[:6]}"
            error_event = StreamEvent(id=message_id, event=StreamEventType.ERROR, content=f"API Error before stream start: {str(e)}")
            try:
                logger.debug(f"[_handle_stream] Attempting to put ERROR event to queue.")
                queue.put_nowait(error_event.model_dump_json())
                logger.debug(f"[_handle_stream] Successfully put ERROR event.")
            except Full:
                logger.error(f"[_handle_stream] Stream queue full. Discarding ERROR event.")
            except Exception as q_err:
                 logger.error(f"[_handle_stream] Error putting ERROR event to queue: {q_err}", exc_info=True)
        return f"API Call Error: {str(e)}", None
    except Exception as e:
        logger.error(f"Unexpected Error calling {provider_name} (model: {model_to_use}): {e}", exc_info=True)
        if is_streaming and queue:
            message_id = f"msg_{uuid.uuid4().hex[:6]}"
            error_event = StreamEvent(id=message_id, event=StreamEventType.ERROR, content=f"Unexpected Error before stream start: {str(e)}")
            try:
                logger.debug(f"[_handle_stream] Attempting to put ERROR event to queue.")
                queue.put_nowait(error_event.model_dump_json())
                logger.debug(f"[_handle_stream] Successfully put ERROR event.")
            except Full:
                logger.error(f"[_handle_stream] Stream queue full. Discarding ERROR event.")
            except Exception as q_err:
                 logger.error(f"[_handle_stream] Error putting ERROR event to queue: {q_err}", exc_info=True)
        return f"Unexpected Error processing request: {str(e)}", None


def call_direct_llm_api(
        prompt: str,
        provider: str = "deepseek",  # 可选值: "deepseek" 或 "openrouter"
        model: str = None,
        temperature: float = 0.0,
        max_tokens: int = 4096
) -> Tuple[str, Optional[str]]:
    """
    直接调用 LLM API 并提取思维链

    Args:
        prompt: 提示词
        provider: API 提供商，可选 "deepseek" 或 "openrouter"
        model: 模型名称，默认使用环境变量中配置的模型
        temperature: 温度参数
        max_tokens: 最大生成 token 数

    Returns:
        Tuple[str, Optional[str]]: (生成内容, 思维链)
    """

    if provider.lower() == "openrouter":
        api_key = os.getenv("ANALYZE_TEXT_LLM_API_KEY")
        base_url = os.getenv("ANALYZE_TEXT_LLM_API_BASE")
        model = model or "deepseek/deepseek-r1"  # OpenRouter 的默认模型
        provider_name = "OpenRouter"

        if not api_key:
            logger.error("缺少 OpenRouter API 配置，请检查环境变量 OPENROUTER_API_KEY")
            return "API 配置错误，无法调用 LLM", None

        # 创建 OpenAI 客户端，并设置 OpenRouter 特有的请求头
        client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            default_headers={
                "HTTP-Referer": "https://prime-rag-api.example.com",
                "X-Title": "Prime RAG API"
            }
        )

        # 不需要额外参数
        extra_args = {}
    else:
        logger.error(f"不支持的 API 提供商: {provider}")
        return f"不支持的 API 提供商: {provider}", None

    try:
        logger.info(f"正在调用 {provider_name} API (模型: {model})")

        # 发送请求
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            temperature=temperature,
            max_tokens=max_tokens,
            **extra_args
        )

        # 记录原始响应（用于调试）
        if hasattr(response, 'model_dump_json'):
            # 新版 OpenAI 库
            response_json = json.loads(response.model_dump_json())
            logger.debug(f"{provider_name} API 原始响应: {json.dumps(response_json, ensure_ascii=False)}")
        else:
            # 兼容处理
            logger.debug(f"{provider_name} API 原始响应类型: {type(response)}")

        # 提取内容
        content = response.choices[0].message.content

        # 提取思维链
        thinking_process = None
        message = response.choices[0].message

        # 1. 尝试从 message 中提取思维链
        for key in ["reasoning_content", "reasoning", "thinking"]:
            if hasattr(message, key) and getattr(message, key):
                thinking_process = getattr(message, key)
                logger.info(f"从 {provider_name} API 响应中提取到思维链 (字段: {key})")
                break

        # 2. 如果直接属性不存在，尝试从 message.model_extra 中提取
        if not thinking_process and hasattr(message, 'model_extra') and message.model_extra:
            for key in ["reasoning_content", "reasoning", "thinking"]:
                if key in message.model_extra:
                    thinking_process = message.model_extra[key]
                    logger.info(f"从 message.model_extra 中提取到思维链 (字段: {key})")
                    break

        # 3. 如果还是没有找到，尝试从原始响应中提取
        if not thinking_process and hasattr(response, '_raw_response'):
            raw = response._raw_response
            if hasattr(raw, 'choices') and len(raw.choices) > 0 and hasattr(raw.choices[0], 'message'):
                raw_message = raw.choices[0].message
                for key in ["reasoning_content", "reasoning", "thinking"]:
                    if hasattr(raw_message, key) and getattr(raw_message, key):
                        thinking_process = getattr(raw_message, key)
                        logger.info(f"从 _raw_response 中提取到思维链 (字段: {key})")
                        break

        # 4. 如果还是没有找到，尝试从 response.choices[0] 中提取
        if not thinking_process and hasattr(response.choices[0], 'message'):
            choice = response.choices[0]
            if hasattr(choice, 'message'):
                for key in ["reasoning_content", "reasoning", "thinking"]:
                    if hasattr(choice, key) and getattr(choice, key):
                        thinking_process = getattr(choice, key)
                        logger.info(f"从 response.choices[0] 中提取到思维链 (字段: {key})")
                        break

        return content, thinking_process

    except Exception as e:
        logger.error(f"调用 {provider_name} API 时发生错误: {e}")
        return f"API 调用错误: {str(e)}", None
