"""
定义各种数据模型和Schema
"""
from typing import Dict, List, Optional, Any, Union, Literal
from pydantic import BaseModel, Field
from app.domain.icrc_intent_entity import FillFormDetails
from enum import Enum
import uuid

class BaseResponse(BaseModel):
    """所有工具返回的标准响应格式"""
    code: int = Field(0, description="状态码，0表示成功，其他值表示特定错误")
    message: str = Field("OK", description="状态描述，成功或错误信息")
    data: Optional[Dict[str, Any]] = Field(None, description="返回的实际数据，无数据时为空对象或null")


class AmapDistrictItem(BaseModel):
    """高德行政区划信息项"""
    citycode: Optional[str] = None
    adcode: str
    name: str
    center: Optional[str] = None
    level: str
    districts: List['AmapDistrictItem'] = []


class AmapDistrictData(BaseModel):
    """高德行政区划返回数据"""
    districts: List[AmapDistrictItem] = []
    count: Optional[str] = None
    suggestion: Optional[Dict[str, Any]] = None


class AmapDistrictResponse(BaseResponse):
    """高德行政区划API完整响应"""
    data: Optional[AmapDistrictData] = None


class AmapWeatherLiveItem(BaseModel):
    """高德天气实况信息项"""
    province: str
    city: str
    adcode: str
    weather: str
    temperature: str
    winddirection: Optional[str] = None
    windpower: Optional[str] = None
    humidity: Optional[str] = None
    reporttime: str
    temperature_float: Optional[str] = None
    humidity_float: Optional[str] = None


class AmapWeatherForecastItem(BaseModel):
    """高德天气预报信息项"""
    province: str
    city: str
    adcode: str
    reporttime: str
    casts: List[Dict[str, Any]] = []


class AmapWeatherData(BaseModel):
    """高德天气返回数据"""
    lives: Optional[List[AmapWeatherLiveItem]] = None
    forecasts: Optional[List[AmapWeatherForecastItem]] = None


class AmapWeatherResponse(BaseResponse):
    """高德天气API完整响应"""
    data: Optional[AmapWeatherData] = None


# 时间查询响应模型
class TimeResponse(BaseResponse):
    """当前时间查询API完整响应"""
    data: Optional[str] = Field(None, description="格式化后的时间字符串 (YYYY-MM-DD HH:MM:SS) 或 null")


# 输入模型
class AmapDistrictInput(BaseModel):
    """高德行政区划查询输入参数"""
    keywords: str = Field(..., description="查询的关键词，如城市名、adcode 或 citycode")
    subdistrict: int = Field(1, description="查询的子行政区级数 (0-3)")


class AmapWeatherInput(BaseModel):
    """高德天气查询输入参数"""
    city: str = Field(..., description="需要查询天气城市的 adcode")
    extensions: str = Field("base", description="气象类型。可选值：base/all；base:返回实况天气, all:返回预报天气")


# 应用启动参数模型
import json
from typing import List, Optional, Dict, Any
from pydantic import BaseModel


# --- Nested Models Based on JSON Structure ---

class ChatMessage(BaseModel):
    role: str
    content: str


class SceneInfo(BaseModel):
    sceneName: str


class Record(BaseModel):
    content: str
    recordId: str


class VisitData(BaseModel):
    docId: str
    isStruct: bool
    tableNameDesc: str
    tableName: str
    records: List[Record]


class Visit(BaseModel):
    visitId: int
    visitType: str
    data: List[VisitData]


class PatientInfo(BaseModel):
    ptsId: Optional[str] = None
    projectId: Optional[str] = None
    projectName: Optional[str] = None
    empiId: Optional[str] = None
    hospitalNo: Optional[str] = None
    designVersionId: Optional[str] = None


class StageInfo(BaseModel):
    stageId: Optional[str] = None


class Context(BaseModel):
    patientInfo: Optional[PatientInfo]
    stageInfo: Optional[StageInfo] = None


# 添加到schemas.py文件中

class DataSource(BaseModel):
    docId: Optional[str] = None
    recordId: Optional[str] = None
    hitValue: Optional[str] = None
    tableNameDesc: Optional[str] = None
    tableName: Optional[str] = None
    columnName: Optional[str] = None
    startPointIndex: Optional[int] = None
    endPointIndex: Optional[int] = None


class CrfIndex(BaseModel):
    crfIndexName: str
    crfIndexCode: str
    crfIndexType: str
    # Value can be different types, using Optional[Any] or keep as str if always string initially
    crfIndexValue: Optional[str] = None  # Based on the example where it's empty string
    dataSource: Optional[List[DataSource]] = None


class CrfTable(BaseModel):
    crfTableName: str
    crfTableCode: str
    crfGroupId: str
    crfIndexList: List[CrfIndex]
    visitId: Optional[str] = None  # 添加visitId字段


class CrfInfo(BaseModel):
    tableList: List[CrfTable]


class KnowledgeFile(BaseModel):
    fileId: str
    collectionName: str
    fileName: str
    fileUrl: Optional[str] = None  # Added from Java Knowledge
    fileType: Optional[str] = None  # Added from Java Knowledge


class MetaInfo(BaseModel):
    # Fields corresponding to AgentApiRequest
    sceneInfo: SceneInfo
    context: Optional[Context] = None

    # Fields corresponding to CommonApiRequest
    visits: List[Visit]
    isRepeat: int
    crfInfo: CrfInfo
    knowledge: Optional[List[KnowledgeFile]] = None
    requestId: Optional[str] = None
    ptsId: Optional[str] = None
    groupId: Optional[str] = None
    proVisitId: Optional[str] = None
    projectId: Optional[str] = None
    sourcedata: Optional[List[Dict[str, Any]]] = None
    rule: Optional[str] = None
    siteInfo: Optional[str] = None
    siteId: Optional[int] = None  # Java Long maps to int in Python


# --- Updated AssistantAppParams Model ---

class AssistantAppParams(BaseModel):
    chatType: Optional[str] = Field("standard", description="聊天类型 (e.g., 'standard')")
    questions: Optional[List[ChatMessage]] = Field(default_factory=list, description="聊天历史记录")
    metaInfo: Optional[MetaInfo] = Field(None, description="包含场景、就诊记录、CRF表单、知识库等元信息")

# --- Example Usage (for verification) ---

# Corresponds to Java KnowledgeSlice.Position
class Position(BaseModel):
    context: Optional[str] = None
    pageNo: Optional[int] = None
    start: Optional[int] = None
    end: Optional[int] = None


# Corresponds to Java KnowledgeSlice
class KnowledgeSlice(BaseModel):
    fileId: Optional[str] = None
    fileName: Optional[str] = None
    fileUrl: Optional[str] = None
    tag: Optional[int] = None
    start: Optional[int] = None
    end: Optional[int] = None
    context: Optional[str] = None
    position: Optional[Position] = None


# Corresponds to Java IJudgeCrfResult.CrfTableValueItem
class CrfTableValueItem(BaseModel):
    crfIndexName: Optional[str] = None
    crfIndexValue: Optional[str] = None
    crfItemId: Optional[int] = None  # Java Long maps to int
    crfIndexCode: Optional[str] = None


# Corresponds to Java IJudgeCrfResult
class IJudgeCrfResult(BaseModel):
    crfTableName: Optional[str] = None
    csJudgement: Optional[int] = None
    csJudgementDsc: Optional[str] = None
    crfTableValue: Optional[List[CrfTable]] = None


# 状态模型
class AssistantState(BaseModel):
    """助手状态 - Pydantic 模型"""
    intent: Optional[str] = None
    location: Optional[str] = None
    user_name: Optional[str] = None
    adcode: Optional[str] = None
    district_result: Optional[AmapDistrictResponse] = None
    weather_result: Optional[AmapWeatherResponse] = None
    time_result: Optional[TimeResponse] = None
    user_info_result: Optional[BaseResponse] = None
    error_message: Optional[str] = None
    final_response: Optional[str] = None
    app_params: Optional[AssistantAppParams] = None
    icrc_intent: Optional[Literal["Fill_Form", "Refill_Form", "other"]] = Field(None, description="识别出的ICRC意图")
    icrc_source: Optional[Literal["Structure", "Paper", "Medical_Record"]] = Field(None, description="Refill_Form的数据来源")
    icrc_extracted_details: Optional[FillFormDetails] = Field(None, description="提取出的表单填写细节")
    think_logic: Optional[str] = None
    think_data_sources: Optional[List[DataSource]] = None
    knowledge_slice: Optional[List[KnowledgeSlice]] = None
    recommended_question: Optional[List[str]] = None
    crf_result: Optional[List[IJudgeCrfResult]] = None
    thought_log: Optional[List[str]] = Field(default_factory=list, description="逐步累积的思考过程日志")
    # 添加用户上传知识相关字段
    user_uploaded_knowledge: Optional[List[KnowledgeFile]] = Field(default_factory=list, description="用户上传的知识文件列表")
    user_uploaded_knowledge_content: Optional[Dict[str, str]] = Field(default_factory=dict, description="用户上传的知识内容映射，键为fileId，值为内容")

    # Pydantic configuration if needed (e.g., for arbitrary types)
    # class Config:
    #     arbitrary_types_allowed = True # 确保枚举值被序列化为字符串

class StreamEventType(str, Enum):
    """流式响应事件类型"""
    CONNECTING = "connecting"
    THINKING = "thinking"
    ANSWERING = "answering"
    COMPLETE = "complete"
    ERROR = "error" # 添加错误事件类型

class StreamEvent(BaseModel):
    """流式响应事件模型"""
    id: str = Field(default_factory=lambda: f"msg_{uuid.uuid4().hex[:6]}", description="消息的唯一ID")
    event: StreamEventType = Field(description="事件类型")
    content: Optional[Any] = Field(default=None, description="事件内容，可以是字符串或其他数据")
    # 添加一个 data 字段，用于 complete 事件时返回额外数据
    data: Optional[Any] = Field(default=None, description="完成事件可能携带的额外数据")

    class Config:
        use_enum_values = True # 确保枚举值被序列化为字符串


class CrfIndexAnswer(BaseModel):
    crf_index_name: str
    crf_index_code: str
    crf_index_value: str
    dataSource: List[DataSource] = []


class CrfTableAnswerValue(BaseModel):
    answer_id: int = 0
    visit_id: Optional[str] = None
    answer_value: List[CrfIndexAnswer] = []


class CrfTableResult(BaseModel):
    crf_table_name: str
    crf_table_code: str
    crf_table_value_type: int = 0
    crf_group_id: Optional[str] = None
    crf_table_value: List[CrfTableAnswerValue] = []


class Position(BaseModel):
    context: Optional[str] = None
    pageNo: Optional[int] = None
    start: Optional[int] = None
    end: Optional[int] = None


class KnowledgeSliceItem(BaseModel):
    fileId: str
    fileName: str
    fileUrl: Optional[str] = None
    tag: int = 1
    start: int = 0
    end: int = 0
    context: str
    position: Optional[Position] = None


class IntentInfo(BaseModel):
    primary: str
    secondary: Optional[str] = None
    confidence: Optional[float] = None
    modelName: str


class CustomApiResponse(BaseModel):
    request_id: str
    timestamp: str
    intent: IntentInfo
    answer: str
    crf_result: List[CrfTableResult] = []
    knowledge_slice: List[KnowledgeSliceItem] = []
    thinkLogic: Optional[str] = None
    visits: Optional[List[Visit]] = None


class StandardResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: Optional[CustomApiResponse] = None

class PvAssistantAppParams(AssistantAppParams):
    prompt: str
    provider: Optional[str] = None
    model: Optional[str] = None
