import os
from dotenv import load_dotenv
import logging

# Configure basic logging with detailed timestamp
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s.%(msecs)03d - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))

# --- LLM Configuration ---
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
# OpenRouter 配置
OPENROUTER_API_BASE = os.getenv("OPENROUTER_API_BASE", "https://openrouter.ai/api/v1")
# 使用 OpenRouter 兼容的模型
LLM_MODEL_NAME = os.getenv("LLM_MODEL_NAME", "anthropic/claude-3-opus:beta")

# --- Milvus Configuration ---
MILVUS_USE_REMOTE_STR = os.getenv("MILVUS_USE_REMOTE", 'false').lower()
MILVUS_USE_REMOTE = MILVUS_USE_REMOTE_STR == 'true'

MILVUS_COLLECTION_NAME = os.getenv("MILVUS_COLLECTION_NAME", "icd10_knowledge")
MILVUS_LITE_FILE = os.getenv("MILVUS_LITE_FILE", "./milvus_lite_data.db")
MILVUS_REMOTE_URI = os.getenv("MILVUS_REMOTE_URI")
MILVUS_REMOTE_TOKEN = os.getenv("MILVUS_REMOTE_TOKEN")
MILVUS_DIM = 384 # Dimension for sentence-transformers/all-MiniLM-L6-v2
MILVUS_INSERT_BATCH_SIZE = int(os.getenv("MILVUS_INSERT_BATCH_SIZE", 50))

# --- Embedding Configuration ---
EMBEDDING_MODEL_NAME = os.getenv("EMBEDDING_MODEL_NAME", "sentence-transformers/all-MiniLM-L6-v2")
# Hugging Face 远程 embedding 配置
USE_HF_REMOTE_EMBEDDING_STR = os.getenv("USE_HF_REMOTE_EMBEDDING", 'false').lower()
USE_HF_REMOTE_EMBEDDING = USE_HF_REMOTE_EMBEDDING_STR == 'true'
HF_API_TOKEN = os.getenv("HF_API_TOKEN")

# --- Data Configuration ---
_default_csv_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'data', 'icd10cm_codes_2025.csv'))
INPUT_CSV_PATH = os.getenv("INPUT_CSV_PATH", _default_csv_path)
PROCESSING_BATCH_SIZE = int(os.getenv("PROCESSING_BATCH_SIZE", 100))

# --- Prompt Configuration ---
PROMPT_FILE_PATH = os.getenv("PROMPT_FILE_PATH")
QUERY_PROMPT_FILE_PATH = os.getenv("QUERY_PROMPT_FILE_PATH")
if not PROMPT_FILE_PATH:
    raise ValueError("PROMPT_FILE_PATH must be set in your .env file! Example: PROMPT_FILE_PATH='/Users/<USER>/ask_code/prompts/prompt_en.md'")
if not QUERY_PROMPT_FILE_PATH:
    raise ValueError("QUERY_PROMPT_FILE_PATH must be set in your .env file! Example: QUERY_PROMPT_FILE_PATH='/Users/<USER>/ask_code/prompts/query_prompt.md'")

# --- Validation and Logging ---

def load_config():
    """兼容 cli_search.py，返回 config 模块本身作为配置对象。"""
    return __import__(__name__)

def validate_config():
    """Validates required configuration and logs settings."""
    required_llm = ['OPENROUTER_API_KEY', 'LLM_MODEL_NAME'] # Adjust if not using OpenAI
    missing_llm = [key for key in required_llm if not globals().get(key)]
    if missing_llm:
        logger.warning(f"Missing required LLM config: {', '.join(missing_llm)}. LLM features might fail.")

    if MILVUS_USE_REMOTE:
        if not MILVUS_REMOTE_URI:
            logger.error("MILVUS_USE_REMOTE is true, but MILVUS_REMOTE_URI is not set.")
            raise ValueError("Missing MILVUS_REMOTE_URI for remote Milvus connection.")
        logger.info("Configured to use REMOTE Milvus.")
        logger.info(f"  URI: {MILVUS_REMOTE_URI}")
        logger.info(f"  Token: {'Set' if MILVUS_REMOTE_TOKEN else 'Not Set'}")
    else:
        logger.info("Configured to use LOCAL Milvus Lite.")
        logger.info(f"  Data file: {MILVUS_LITE_FILE}")

    logger.info(f"Milvus Collection: {MILVUS_COLLECTION_NAME}")
    logger.info(f"Milvus Vector Dimension: {MILVUS_DIM}")
    logger.info(f"Milvus Insert Batch Size: {MILVUS_INSERT_BATCH_SIZE}")
    logger.info(f"Embedding Model: {EMBEDDING_MODEL_NAME}")
    logger.info(f"Using HF Remote Embedding: {USE_HF_REMOTE_EMBEDDING}")
    logger.info(f"HF API Token: {'Set' if HF_API_TOKEN else 'Not Set'}")
    logger.info(f"LLM Model: {LLM_MODEL_NAME}")
    # logger.info(f"Input CSV: {INPUT_CSV_PATH}")
    logger.info(f"Processing Batch Size: {PROCESSING_BATCH_SIZE}")
    logger.info(f"Prompt File: {PROMPT_FILE_PATH}")

    # Check if input files exist
    # if not os.path.exists(INPUT_CSV_PATH):
    #     logger.error(f"Input CSV file not found: {INPUT_CSV_PATH}")
    #     raise FileNotFoundError(f"Input CSV file not found: {INPUT_CSV_PATH}")
    if not os.path.exists(PROMPT_FILE_PATH):
        logger.error(f"Prompt file not found: {PROMPT_FILE_PATH}")
        raise FileNotFoundError(f"Prompt file not found: {PROMPT_FILE_PATH}")

    return True

# Call validation on import (optional, can be called explicitly in main.py)
# try:
#     validate_config()
# except (ValueError, FileNotFoundError) as e:
#     logger.critical(f"Configuration error: {e}")
#     # Decide how to handle fatal config errors, e.g., exit
#     # sys.exit(1)
