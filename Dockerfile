# 使用官方 Python 镜像作为基础镜像
FROM harbor.primelifescience.com.cn/prime-infra/python:3.9-slim
#FROM harbor.primelifescience.com.cn/prime-direct/rag-api:feat-cicd_281388-250430_015331

# 设置工作目录
WORKDIR /app

# 配置 pip 使用清华源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    && pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3-venv \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt /app/
RUN python -m venv /app/venv \
    && /app/venv/bin/pip install --upgrade pip \
    && /app/venv/bin/pip install -r requirements.txt

# 复制项目文件到容器中
COPY . /app/

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PATH="/app/venv/bin:$PATH"

# 暴露应用端口（根据应用需要调整）
EXPOSE 8000

# 设置容器启动命令
# 使用虚拟环境中的 Python 解释器运行应用
CMD ["/app/venv/bin/python", "run.py"]